<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار واجهة الهاتف المحمول - Loacker</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #d50000;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            border-left: 3px solid #d50000;
        }
        
        .info-item strong {
            display: block;
            margin-bottom: 5px;
        }
        
        button {
            background-color: #d50000;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background-color: #9b0000;
        }
        
        .mobile-simulation {
            border: 2px solid #333;
            border-radius: 20px;
            width: 375px;
            height: 667px;
            margin: 20px auto;
            overflow: hidden;
            position: relative;
            background: #000;
            padding: 10px;
        }
        
        .mobile-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 اختبار واجهة الهاتف المحمول - Loacker</h1>
        
        <div class="test-section">
            <h3>📱 معلومات الجهاز الحالي</h3>
            <div id="device-info">
                <div class="info-grid">
                    <div class="info-item">
                        <strong>نوع الجهاز:</strong>
                        <span id="device-type">جاري التحقق...</span>
                    </div>
                    <div class="info-item">
                        <strong>حجم الشاشة:</strong>
                        <span id="screen-size">جاري التحقق...</span>
                    </div>
                    <div class="info-item">
                        <strong>User Agent:</strong>
                        <span id="user-agent">جاري التحقق...</span>
                    </div>
                    <div class="info-item">
                        <strong>كلاسات Body:</strong>
                        <span id="body-classes">جاري التحقق...</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبارات الكشف</h3>
            <div id="detection-tests">
                <div id="mobile-detection" class="status warning">
                    <strong>كشف الأجهزة المحمولة:</strong> جاري الاختبار...
                </div>
                <div id="css-application" class="status warning">
                    <strong>تطبيق CSS:</strong> جاري الاختبار...
                </div>
                <div id="interface-visibility" class="status warning">
                    <strong>ظهور الواجهة:</strong> جاري الاختبار...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 أدوات الاختبار</h3>
            <button onclick="simulateMobile()">محاكاة جهاز محمول</button>
            <button onclick="simulateDesktop()">محاكاة جهاز كمبيوتر</button>
            <button onclick="refreshTests()">إعادة تشغيل الاختبارات</button>
            <button onclick="openMainApp()">فتح التطبيق الرئيسي</button>
        </div>
        
        <div class="test-section">
            <h3>📱 محاكي الهاتف المحمول</h3>
            <div class="mobile-simulation">
                <div class="mobile-screen">
                    <iframe id="mobile-frame" src="about:blank"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            runTests();
        });
        
        function updateDeviceInfo() {
            document.getElementById('device-type').textContent = getDeviceType();
            document.getElementById('screen-size').textContent = `${window.innerWidth}x${window.innerHeight}`;
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 100) + '...';
            document.getElementById('body-classes').textContent = document.body.className || 'لا توجد كلاسات';
        }
        
        function getDeviceType() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isTablet = /iPad|Android.*Tablet|Windows.*Touch/i.test(navigator.userAgent);
            
            if (isMobile && !isTablet) return 'هاتف محمول';
            if (isTablet) return 'جهاز لوحي';
            return 'كمبيوتر مكتبي';
        }
        
        function runTests() {
            // اختبار كشف الأجهزة المحمولة
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const mobileDetectionEl = document.getElementById('mobile-detection');
            
            if (isMobile) {
                mobileDetectionEl.className = 'status success';
                mobileDetectionEl.innerHTML = '<strong>كشف الأجهزة المحمولة:</strong> ✅ تم كشف جهاز محمول';
            } else {
                mobileDetectionEl.className = 'status error';
                mobileDetectionEl.innerHTML = '<strong>كشف الأجهزة المحمولة:</strong> ❌ لم يتم كشف جهاز محمول';
            }
            
            // اختبار تطبيق CSS
            const hasMobileClass = document.body.classList.contains('mobile-device');
            const cssApplicationEl = document.getElementById('css-application');
            
            if (hasMobileClass) {
                cssApplicationEl.className = 'status success';
                cssApplicationEl.innerHTML = '<strong>تطبيق CSS:</strong> ✅ تم تطبيق كلاس mobile-device';
            } else {
                cssApplicationEl.className = 'status error';
                cssApplicationEl.innerHTML = '<strong>تطبيق CSS:</strong> ❌ لم يتم تطبيق كلاس mobile-device';
            }
            
            // اختبار ظهور الواجهة
            const interfaceVisibilityEl = document.getElementById('interface-visibility');
            interfaceVisibilityEl.className = 'status warning';
            interfaceVisibilityEl.innerHTML = '<strong>ظهور الواجهة:</strong> ⚠️ يتطلب فتح التطبيق الرئيسي للاختبار';
        }
        
        function simulateMobile() {
            // إضافة كلاس mobile-device يدوياً للاختبار
            document.body.classList.add('mobile-device');
            updateDeviceInfo();
            runTests();
            alert('تم محاكاة جهاز محمول! تحقق من النتائج أعلاه.');
        }
        
        function simulateDesktop() {
            // إزالة كلاس mobile-device للاختبار
            document.body.classList.remove('mobile-device');
            updateDeviceInfo();
            runTests();
            alert('تم محاكاة جهاز كمبيوتر! تحقق من النتائج أعلاه.');
        }
        
        function refreshTests() {
            updateDeviceInfo();
            runTests();
        }
        
        function openMainApp() {
            // فتح التطبيق الرئيسي في نافذة جديدة
            window.open('/', '_blank');
        }
    </script>
</body>
</html>
