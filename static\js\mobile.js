/**
 * JavaScript لواجهة الهاتف النقال
 * تم تحسينه: 2024
 */

// نمط الوحدة (Module Pattern) لتنظيم الكود
const MobileApp = (() => {
    // المتغيرات الخاصة
    let mobileMap = null;
    let mobileSelectedLocation = null;
    let mobileMarkers = [];
    let mobileStores = [];
    let mobileCurrentListId = null;
    let mobileLists = [];
    let isLoading = false;
    let mapInitialized = false;
    let initializationInProgress = false;
    let mapContainer = null;

    // وظائف مساعدة
    const utils = {
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        throttle(func, limit) {
            let inThrottle;
            return function executedFunction(...args) {
                if (!inThrottle) {
                    func(...args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        showLoading() {
            isLoading = true;
            const loader = document.createElement('div');
            loader.className = 'mobile-loader';
            loader.innerHTML = '<div class="spinner"></div>';
            document.body.appendChild(loader);
        },

        hideLoading() {
            isLoading = false;
            const loader = document.querySelector('.mobile-loader');
            if (loader) {
                loader.remove();
            }
        },

        cleanupMap() {
            if (mobileMap) {
                try {
                    // إزالة جميع الطبقات
                    mobileMap.eachLayer((layer) => {
                        mobileMap.removeLayer(layer);
                    });
                    // إزالة الخريطة
                    mobileMap.remove();
                } catch (error) {
                    console.warn('Error cleaning up map:', error);
                }
                mobileMap = null;
                mapInitialized = false;
                mapContainer = null;
            }
        },

        async waitForElement(selector, timeout = 5000) {
            const startTime = Date.now();
            while (Date.now() - startTime < timeout) {
                const element = document.querySelector(selector);
                if (element) return element;
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            throw new Error(`Element ${selector} not found after ${timeout}ms`);
        },

        isMapContainerInitialized() {
            return document.querySelector('#mobile-map .leaflet-container') !== null;
        },

        showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'mobile-error';
            errorDiv.textContent = message;
            document.body.appendChild(errorDiv);
            setTimeout(() => errorDiv.remove(), 3000);
        },

        async destroyMap() {
            if (mobileMap) {
                try {
                    // إزالة جميع الطبقات
                    mobileMap.eachLayer((layer) => {
                        mobileMap.removeLayer(layer);
                    });
                    // إزالة الخريطة
                    mobileMap.remove();
                    mobileMap = null;
                } catch (error) {
                    console.warn('Error destroying map:', error);
                }
            }
            mapInitialized = false;
            initializationInProgress = false;
        },

        async recreateMapContainer() {
            const mapContainer = document.getElementById('mobile-map');
            if (mapContainer) {
                // حفظ الأب
                const parent = mapContainer.parentNode;
                // إزالة الحاوية القديمة
                parent.removeChild(mapContainer);
                // إنشاء حاوية جديدة
                const newContainer = document.createElement('div');
                newContainer.id = 'mobile-map';
                newContainer.className = 'mobile-map';
                parent.appendChild(newContainer);
                return newContainer;
            }
            return null;
        }
    };

    // إدارة الخريطة
    const mapManager = {
        async init() {
            if (initializationInProgress) {
                console.log('Map initialization already in progress');
                return false;
            }

            try {
                initializationInProgress = true;

                // تدمير الخريطة القديمة إذا وجدت
                await utils.destroyMap();

                // إعادة إنشاء حاوية الخريطة
                const mapContainer = await utils.recreateMapContainer();
                if (!mapContainer) {
                    throw new Error('Failed to create map container');
                }

                // إنشاء الخريطة الجديدة
                mobileMap = L.map('mobile-map', {
                    center: [32.8872, 13.1913],
                    zoom: 13,
                    zoomControl: false,
                    attributionControl: false,
                    preferCanvas: true
                });

                // إضافة طبقة الخريطة الأساسية
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    maxZoom: 19
                }).addTo(mobileMap);

                // إضافة عناصر التحكم
                L.control.zoom({
                    position: 'bottomright'
                }).addTo(mobileMap);

                // إضافة طبقة القمر الصناعي
                const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    maxZoom: 19
                });

                // إضافة طبقات الخريطة
                const baseMaps = {
                    "الخريطة العادية": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        maxZoom: 19
                    }),
                    "القمر الصناعي": satelliteLayer
                };

                L.control.layers(baseMaps, null, {
                    position: 'bottomright'
                }).addTo(mobileMap);

                // إضافة مستمع النقر
                mobileMap.on('click', function(e) {
                    if (mobileSelectedLocation) {
                        mobileMap.removeLayer(mobileSelectedLocation);
                    }
                    mobileSelectedLocation = L.marker(e.latlng).addTo(mobileMap);
                    document.getElementById('mobile-selected-location').textContent = 
                        `تم تحديد الموقع: ${e.latlng.lat.toFixed(6)}, ${e.latlng.lng.toFixed(6)}`;
                });

                mapInitialized = true;
                return true;
            } catch (error) {
                console.error('Failed to initialize map:', error);
                await utils.destroyMap();
                return false;
            } finally {
                initializationInProgress = false;
            }
        },

        async addMapLayers() {
            if (!mobileMap) return;

            const layers = {
                streets: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
                }),
                satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: 'Tiles &copy; Esri'
                }),
                terrain: L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png', {
                    attribution: 'Map tiles by Stamen Design'
                })
            };

            const baseMaps = {
                "الخريطة": layers.streets,
                "القمر الصناعي": layers.satellite,
                "التضاريس": layers.terrain
            };

            L.control.layers(baseMaps).addTo(mobileMap);
            layers.streets.addTo(mobileMap);
        },

        addMapControls() {
            L.control.zoom({
                position: 'bottomright'
            }).addTo(mobileMap);
        },

        setupMapEventListeners() {
            if (!mobileMap) return;

            // تحسين أداء النقر على الخريطة
            mobileMap.on('click', utils.throttle((e) => {
                this.setSelectedLocation(e.latlng);
            }, 250));

            // تحسين أداء تحريك الخريطة
            mobileMap.on('moveend', utils.debounce(() => {
                this.updateVisibleMarkers();
            }, 150));
        },

        setSelectedLocation(latlng) {
            mobileSelectedLocation = latlng;
            // تحديث واجهة المستخدم
            this.updateSelectedLocationUI();
        },

        updateSelectedLocationUI() {
            // تحديث واجهة المستخدم للموقع المحدد
            const locationElement = document.querySelector('.selected-location');
            if (locationElement && mobileSelectedLocation) {
                locationElement.textContent = `${mobileSelectedLocation.lat.toFixed(6)}, ${mobileSelectedLocation.lng.toFixed(6)}`;
            }
        },

        updateVisibleMarkers() {
            if (!mobileMap) return;
            
            const bounds = mobileMap.getBounds();
            mobileMarkers.forEach(marker => {
                const isVisible = bounds.contains(marker.getLatLng());
                marker.setOpacity(isVisible ? 1 : 0.5);
            });
        }
    };

    // إدارة المتاجر
    const storeManager = {
        async loadStores(listId = null) {
            utils.showLoading();
            try {
                // التحقق من وجود API endpoint
                const apiUrl = `/api/stores${listId ? `?list=${listId}` : ''}`;
                console.log('Loading stores from:', apiUrl);

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (!data || !Array.isArray(data.stores)) {
                    throw new Error('Invalid data format received from server');
                }

                mobileStores = data.stores;
                console.log(`Loaded ${mobileStores.length} stores successfully`);

                // تحديث واجهة المستخدم
                await this.renderStores();
                
                // تحديث العلامات على الخريطة إذا كانت الخريطة جاهزة
                if (mobileMap && mapInitialized) {
                    this.updateMarkers();
                }

                return true;
            } catch (error) {
                console.error('Error loading stores:', error);
                utils.showError('حدث خطأ أثناء تحميل المتاجر. يرجى المحاولة مرة أخرى.');
                return false;
            } finally {
                utils.hideLoading();
            }
        },

        async renderStores() {
            try {
                const storeList = document.querySelector('.mobile-store-list');
                if (!storeList) {
                    console.warn('Store list container not found');
                    return;
                }

                // إنشاء fragment لتحسين الأداء
                const fragment = document.createDocumentFragment();
                
                if (mobileStores.length === 0) {
                    const emptyMessage = document.createElement('div');
                    emptyMessage.className = 'mobile-empty-message';
                    emptyMessage.textContent = 'لا توجد متاجر متاحة حالياً';
                    fragment.appendChild(emptyMessage);
                } else {
                    mobileStores.forEach(store => {
                        const storeElement = this.createStoreElement(store);
                        fragment.appendChild(storeElement);
                    });
                }

                // تحديث قائمة المتاجر
                storeList.innerHTML = '';
                storeList.appendChild(fragment);
            } catch (error) {
                console.error('Error rendering stores:', error);
            const fragment = document.createDocumentFragment();
            
            mobileStores.forEach(store => {
                const storeElement = this.createStoreElement(store);
                fragment.appendChild(storeElement);
            });

            storeList.innerHTML = '';
            storeList.appendChild(fragment);
        },

        createStoreElement(store) {
            const div = document.createElement('div');
            div.className = 'mobile-store-card';
            div.innerHTML = `
                <div class="card-body">
                    <h5 class="card-title">${store.name}</h5>
                    <p class="card-subtitle">${store.address}</p>
                    <div class="store-details">
                        <span class="store-type">${store.type || 'متجر'}</span>
                        <span class="store-distance">${store.distance || '0'} كم</span>
                    </div>
                </div>
            `;
            return div;
        },

        updateMarkers() {
            if (!mobileMap) return;
            
            // إزالة العلامات القديمة
            mobileMarkers.forEach(marker => marker.remove());
            mobileMarkers = [];

            // إضافة علامات جديدة
            mobileStores.forEach(store => {
                if (store.latitude && store.longitude) {
                    const marker = L.marker([store.latitude, store.longitude])
                        .bindPopup(`
                            <div class="store-popup">
                                <h4>${store.name}</h4>
                                <p>${store.address}</p>
                            </div>
                        `);
                    marker.addTo(mobileMap);
                    mobileMarkers.push(marker);
                }
            });
        }
    };

    // تهيئة التطبيق
    const init = async () => {
        try {
            utils.showLoading();
            
            // التحقق من وجود العناصر الأساسية
            const requiredElements = [
                '#mobile-map',
                '.mobile-store-list',
                '.mobile-header',
                '.mobile-content'
            ];

            for (const selector of requiredElements) {
                await utils.waitForElement(selector);
            }

            // تهيئة الخريطة
            if (detectDevice()) {
                const mapInitialized = await mapManager.init();
                if (!mapInitialized) {
                    throw new Error('Failed to initialize map');
                }
            }

            // تحميل المتاجر
            await storeManager.loadStores();

            // إعداد مستمعات الأحداث
            setupEventListeners();

            // إظهار واجهة المستخدم
            document.body.classList.add('mobile-ready');
        } catch (error) {
            console.error('Error during initialization:', error);
            utils.showError('حدث خطأ أثناء تهيئة التطبيق');
        } finally {
            utils.hideLoading();
        }
    };

    // إعداد مستمعات الأحداث
    const setupEventListeners = () => {
        // البحث
        const searchInput = document.querySelector('.mobile-search input');
        if (searchInput) {
            searchInput.addEventListener('input', utils.debounce((e) => {
                const searchTerm = e.target.value.trim();
                if (searchTerm) {
                    storeManager.searchStores(searchTerm);
                } else {
                    storeManager.loadStores();
                }
            }, 300));
        }

        // التمرير
        const storeList = document.querySelector('.mobile-store-list');
        if (storeList) {
            storeList.addEventListener('scroll', utils.throttle(() => {
                const scrollPosition = storeList.scrollTop + storeList.clientHeight;
                const scrollHeight = storeList.scrollHeight;
                
                if (scrollPosition >= scrollHeight - 100) {
                    storeManager.loadMoreStores();
                }
            }, 150));
        }
    };

    // كشف الجهاز - الاعتماد على DeviceDetector العام
    const detectDevice = () => {
        // التحقق من وجود DeviceDetector العام
        if (window.deviceDetector) {
            return window.deviceDetector.isMobile;
        }

        // fallback إذا لم يكن DeviceDetector متاحاً
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (isMobile) {
            document.body.classList.add('mobile-device');
        }
        return isMobile;
    };

    // تصدير الواجهة العامة
    return {
        init,
        mapManager,
        storeManager
    };
})();

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // انتظار تهيئة DeviceDetector قبل تهيئة MobileApp
    const initMobileApp = () => {
        if (window.deviceDetector && window.deviceDetector.isMobile) {
            MobileApp.init().catch(error => {
                console.error('Error initializing mobile app:', error);
            });
        } else {
            console.log('Device is not mobile, skipping mobile app initialization');
        }
    };

    // إذا كان DeviceDetector جاهزاً، ابدأ التهيئة
    if (window.deviceDetector) {
        initMobileApp();
    } else {
        // انتظار تهيئة DeviceDetector
        setTimeout(initMobileApp, 100);
    }
});
