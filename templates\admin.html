<!DOCTYPE html>
<html lang="ar" data-bs-theme="dark" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#212529">
    <title>لوحة التحكم - Loacker</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Google Fonts - Tajawal & Playfair Display (Classic European Font) -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Playfair+Display:wght@400;700;900&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Notification Badge CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notification-badge.css') }}">
    <!-- Admin Map CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin-map.css') }}">

    <style>
        .admin-header {
            background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
            padding: 20px 0;
            margin-bottom: 30px;
            border-bottom: 1px solid #333;
        }

        .badge-role {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .badge-admin {
            background-color: #dc3545;
        }

        .badge-marketer {
            background-color: #fd7e14;
        }

        .badge-visitor {
            background-color: #6c757d;
        }

        .text-gradient {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-family: 'Playfair Display', serif;
        }

        .card {
            background-color: #2a2a2a;
            border: 1px solid #333;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: #333;
            border-bottom: 1px solid #444;
            font-weight: bold;
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            border-bottom: 1px solid #444;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h1 class="h4 mb-0">
                        <span class="text-gradient">Loacker</span>
                        <span class="text-muted ms-2">لوحة التحكم</span>
                    </h1>
                </div>
                <div>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-light me-2">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}



        <!-- قسم صلاحيات المستخدمين -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-dark">
                        <h5 class="mb-0"><i class="fas fa-user-lock me-2"></i> صلاحيات المستخدمين</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i> مدير</h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">صلاحية كاملة للنظام</p>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> إضافة وتعديل وحذف المستخدمين</li>
                                            <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> إضافة وتعديل وحذف المتاجر</li>
                                            <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> الوصول إلى لوحة التحكم</li>
                                            <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> تغيير صلاحيات المستخدمين</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0"><i class="fas fa-store me-2"></i> مسوق</h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">صلاحية إدارة المتاجر فقط</p>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item"><i class="fas fa-times-circle text-danger me-2"></i> إضافة وتعديل وحذف المستخدمين</li>
                                            <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> إضافة وتعديل وحذف المتاجر</li>
                                            <li class="list-group-item"><i class="fas fa-times-circle text-danger me-2"></i> الوصول إلى لوحة التحكم</li>
                                            <li class="list-group-item"><i class="fas fa-times-circle text-danger me-2"></i> تغيير صلاحيات المستخدمين</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0"><i class="fas fa-user me-2"></i> زائر</h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">صلاحية عرض المتاجر فقط</p>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item"><i class="fas fa-times-circle text-danger me-2"></i> إضافة وتعديل وحذف المستخدمين</li>
                                            <li class="list-group-item"><i class="fas fa-times-circle text-danger me-2"></i> إضافة وتعديل وحذف المتاجر</li>
                                            <li class="list-group-item"><i class="fas fa-times-circle text-danger me-2"></i> الوصول إلى لوحة التحكم</li>
                                            <li class="list-group-item"><i class="fas fa-times-circle text-danger me-2"></i> تغيير صلاحيات المستخدمين</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم المتاجر المعلقة -->
        <div class="row mb-4" id="pendingStores">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i> المتاجر المعلقة بانتظار الموافقة</h5>
                        <span class="badge bg-danger" id="pendingStoresCount">0</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover" id="pendingStoresTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المتجر</th>
                                        <th>رقم الهاتف</th>
                                        <th>المسوق</th>
                                        <th>الموقع</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم إدارة القوائم المخصصة -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> إدارة القوائم المخصصة</h5>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addListModal">
                            <i class="fas fa-plus me-1"></i> إضافة قائمة
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover" id="listsTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم القائمة</th>
                                        <th>الوصف</th>
                                        <th>عدد المتاجر</th>
                                        <th>المسوقين المخصصين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم نقل المتاجر بين المسوقين -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i> نقل المتاجر بين المسوقين</h5>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#storesTransferModal">
                            <i class="fas fa-list me-1"></i> عرض المتاجر
                        </button>
                    </div>
                    <div class="card-body text-center">
                        <div class="py-4">
                            <i class="fas fa-exchange-alt text-muted" style="font-size: 3rem;"></i>
                            <h6 class="mt-3 text-muted">انقر على "عرض المتاجر" لإدارة نقل المتاجر بين المسوقين</h6>
                            <p class="text-muted small">يمكنك نقل أي متجر من مسوق إلى مسوق آخر بسهولة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم إدارة المدن والمناطق -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i> إدارة المدن والمناطق</h5>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCityModal">
                            <i class="fas fa-plus me-1"></i> إضافة مدينة جديدة
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover" id="citiesTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المدينة</th>
                                        <th>عدد المناطق الفرعية</th>
                                        <th>الإحداثيات</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم إدارة المستخدمين -->
        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i> إدارة المستخدمين</h5>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-1"></i> إضافة مستخدم
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المستخدم</th>
                                        <th>رقم الهاتف</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in users %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ user.username }}</td>
                                        <td>
                                            <a href="https://wa.me/{{ user.phone }}" target="_blank" class="text-decoration-none">
                                                <i class="fab fa-whatsapp text-success me-1"></i> {{ user.phone }}
                                            </a>
                                        </td>
                                        <td>
                                            {% if user.role_id == 1 %}
                                                <span class="badge badge-role badge-admin" data-bs-toggle="tooltip" data-bs-placement="top" title="صلاحية كاملة للنظام (إضافة/تعديل/حذف المستخدمين والمتاجر)">
                                                    <i class="fas fa-user-shield me-1"></i> مدير
                                                </span>
                                            {% elif user.role_id == 2 %}
                                                <span class="badge badge-role badge-marketer" data-bs-toggle="tooltip" data-bs-placement="top" title="صلاحية إضافة وتعديل وحذف المتاجر فقط">
                                                    <i class="fas fa-store me-1"></i> مسوّق
                                                </span>
                                            {% else %}
                                                <span class="badge badge-role badge-visitor" data-bs-toggle="tooltip" data-bs-placement="top" title="صلاحية عرض المتاجر فقط">
                                                    <i class="fas fa-user me-1"></i> زائر
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user.is_active == 1 %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ user.created_at }}</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-primary edit-user-btn"
                                                    data-id="{{ user.id }}"
                                                    data-username="{{ user.username }}"
                                                    data-phone="{{ user.phone }}"
                                                    data-role="{{ user.role_id }}"
                                                    data-active="{{ user.is_active }}"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#editUserModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if current_user and user.id != current_user.id %}
                                            <button type="button" class="btn btn-sm btn-danger delete-user-btn"
                                                    data-id="{{ user.id }}"
                                                    data-username="{{ user.username }}"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#deleteUserModal">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm" method="POST" action="{{ url_for('add_user') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="add_username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="add_username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="add_phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="add_phone" name="phone" placeholder="مثال: 966512345678" required>
                            <div class="form-text">أدخل رقم الهاتف مع رمز الدولة بدون علامة + (مثل: 966512345678)</div>
                        </div>
                        <div class="mb-3">
                            <label for="add_password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="add_password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="add_role" class="form-label">نوع المستخدم</label>
                            <select class="form-select" id="add_role" name="role_id" required>
                                <option value="1">مدير</option>
                                <option value="2">مسوق</option>
                                <option value="3">زائر</option>
                            </select>
                            <div class="form-text text-muted mt-2">
                                <div><strong>مدير:</strong> صلاحية كاملة للنظام (إضافة/تعديل/حذف المستخدمين والمتاجر)</div>
                                <div><strong>مسوق:</strong> صلاحية إضافة وتعديل وحذف المتاجر فقط</div>
                                <div><strong>زائر:</strong> صلاحية عرض المتاجر فقط</div>
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="add_is_active" name="is_active" checked>
                            <label class="form-check-label" for="add_is_active">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="addUserForm" class="btn btn-primary">إضافة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: تعديل مستخدم -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">تعديل مستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm" method="POST" action="{{ url_for('edit_user') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="edit_user_id" name="user_id">
                        <div class="mb-3">
                            <label for="edit_username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="edit_username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="edit_phone" name="phone" placeholder="مثال: 966512345678" required>
                            <div class="form-text">أدخل رقم الهاتف مع رمز الدولة بدون علامة + (مثل: 966512345678)</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_password" class="form-label">كلمة المرور (اتركها فارغة للاحتفاظ بالحالية)</label>
                            <input type="password" class="form-control" id="edit_password" name="password">
                        </div>
                        <div class="mb-3">
                            <label for="edit_role" class="form-label">نوع المستخدم</label>
                            <select class="form-select" id="edit_role" name="role_id" required>
                                <option value="1">مدير</option>
                                <option value="2">مسوق</option>
                                <option value="3">زائر</option>
                            </select>
                            <div class="form-text text-muted mt-2">
                                <div><strong>مدير:</strong> صلاحية كاملة للنظام (إضافة/تعديل/حذف المستخدمين والمتاجر)</div>
                                <div><strong>مسوق:</strong> صلاحية إضافة وتعديل وحذف المتاجر فقط</div>
                                <div><strong>زائر:</strong> صلاحية عرض المتاجر فقط</div>
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="editUserForm" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: حذف مستخدم -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteUserModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    </div>
                    <p>هل أنت متأكد من رغبتك في حذف المستخدم <strong id="delete_username"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                    <form id="deleteUserForm" method="POST" action="{{ url_for('delete_user') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="delete_user_id" name="user_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="deleteUserForm" class="btn btn-danger">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: إضافة قائمة -->
    <div class="modal fade" id="addListModal" tabindex="-1" aria-labelledby="addListModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addListModalLabel">إضافة قائمة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addListForm">
                        <div class="mb-3">
                            <label for="add_list_name" class="form-label">اسم القائمة</label>
                            <input type="text" class="form-control" id="add_list_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="add_list_description" class="form-label">وصف القائمة</label>
                            <textarea class="form-control" id="add_list_description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="addListBtn" class="btn btn-primary">إضافة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: تعديل قائمة -->
    <div class="modal fade" id="editListModal" tabindex="-1" aria-labelledby="editListModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editListModalLabel">تعديل قائمة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editListForm">
                        <input type="hidden" id="edit_list_id">
                        <div class="mb-3">
                            <label for="edit_list_name" class="form-label">اسم القائمة</label>
                            <input type="text" class="form-control" id="edit_list_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_list_description" class="form-label">وصف القائمة</label>
                            <textarea class="form-control" id="edit_list_description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="updateListBtn" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: حذف قائمة -->
    <div class="modal fade" id="deleteListModal" tabindex="-1" aria-labelledby="deleteListModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteListModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    </div>
                    <p>هل أنت متأكد من رغبتك في حذف القائمة <strong id="delete_list_name"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه. سيتم نقل جميع المتاجر في هذه القائمة إلى القائمة الافتراضية.</p>
                    <input type="hidden" id="delete_list_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="deleteListBtn" class="btn btn-danger">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: تخصيص قائمة لمسوق -->
    <div class="modal fade" id="assignListModal" tabindex="-1" aria-labelledby="assignListModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignListModalLabel">تخصيص قائمة لمسوق</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignListForm">
                        <input type="hidden" id="assign_list_id">
                        <div class="mb-3">
                            <label for="assign_list_name" class="form-label">القائمة</label>
                            <input type="text" class="form-control" id="assign_list_name" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="assign_marketer" class="form-label">المسوق</label>
                            <select class="form-select" id="assign_marketer" required>
                                <option value="">اختر مسوق...</option>
                                <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                            </select>
                        </div>
                    </form>
                    <div class="mt-4">
                        <h6>المسوقين المخصصين لهذه القائمة:</h6>
                        <div id="assigned_marketers_list" class="list-group mt-2">
                            <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" id="assignListBtn" class="btn btn-primary">تخصيص</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: إضافة مدينة -->
    <div class="modal fade" id="addCityModal" tabindex="-1" aria-labelledby="addCityModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCityModalLabel">إضافة مدينة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCityForm">
                        <div class="mb-3">
                            <label for="add_city_name" class="form-label">اسم المدينة</label>
                            <input type="text" class="form-control" id="add_city_name" required>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="add_city_lat" class="form-label">خط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="add_city_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="add_city_lng" class="form-label">خط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="add_city_lng" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="add_city_min_lat" class="form-label">الحد الأدنى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="add_city_min_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="add_city_max_lat" class="form-label">الحد الأقصى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="add_city_max_lat" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="add_city_min_lng" class="form-label">الحد الأدنى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="add_city_min_lng" required>
                            </div>
                            <div class="col-md-6">
                                <label for="add_city_max_lng" class="form-label">الحد الأقصى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="add_city_max_lng" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="addCityBtn" class="btn btn-primary">إضافة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: تعديل مدينة -->
    <div class="modal fade" id="editCityModal" tabindex="-1" aria-labelledby="editCityModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCityModalLabel">تعديل مدينة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editCityForm">
                        <input type="hidden" id="edit_city_id">
                        <div class="mb-3">
                            <label for="edit_city_name" class="form-label">اسم المدينة</label>
                            <input type="text" class="form-control" id="edit_city_name" required>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_city_lat" class="form-label">خط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_city_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_city_lng" class="form-label">خط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_city_lng" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_city_min_lat" class="form-label">الحد الأدنى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_city_min_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_city_max_lat" class="form-label">الحد الأقصى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_city_max_lat" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_city_min_lng" class="form-label">الحد الأدنى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_city_min_lng" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_city_max_lng" class="form-label">الحد الأقصى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_city_max_lng" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="updateCityBtn" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: حذف مدينة -->
    <div class="modal fade" id="deleteCityModal" tabindex="-1" aria-labelledby="deleteCityModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteCityModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    </div>
                    <p>هل أنت متأكد من رغبتك في حذف المدينة <strong id="delete_city_name"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع المناطق الفرعية التابعة لهذه المدينة أيضًا.</p>
                    <input type="hidden" id="delete_city_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="deleteCityBtn" class="btn btn-danger">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: إدارة المناطق الفرعية -->
    <div class="modal fade" id="manageDistrictsModal" tabindex="-1" aria-labelledby="manageDistrictsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="manageDistrictsModalLabel">إدارة المناطق الفرعية لمدينة <span id="city_name_title"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between mb-3">
                        <h6>المناطق الفرعية:</h6>
                        <div>
                            <button type="button" class="btn btn-sm btn-success" id="addDistrictGroupBtn">
                                <i class="fas fa-layer-group me-1"></i> إضافة مجموعة مناطق
                            </button>
                            <button type="button" class="btn btn-sm btn-primary ms-2" id="addDistrictBtn">
                                <i class="fas fa-plus me-1"></i> إضافة منطقة فرعية
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="districtsTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المنطقة</th>
                                    <th>الإحداثيات</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: إضافة منطقة فرعية -->
    <div class="modal fade" id="addDistrictModal" tabindex="-1" aria-labelledby="addDistrictModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDistrictModalLabel">إضافة منطقة فرعية جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addDistrictForm">
                        <input type="hidden" id="add_district_city_id">
                        <div class="mb-3">
                            <label for="add_district_name" class="form-label">اسم المنطقة الفرعية</label>
                            <input type="text" class="form-control" id="add_district_name" required>
                            <div class="form-text">سيتم إضافة اسم المدينة تلقائيًا قبل اسم المنطقة الفرعية (مثال: طرابلس - وسط المدينة)</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="add_district_lat" class="form-label">خط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="add_district_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="add_district_lng" class="form-label">خط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="add_district_lng" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="add_district_min_lat" class="form-label">الحد الأدنى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="add_district_min_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="add_district_max_lat" class="form-label">الحد الأقصى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="add_district_max_lat" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="add_district_min_lng" class="form-label">الحد الأدنى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="add_district_min_lng" required>
                            </div>
                            <div class="col-md-6">
                                <label for="add_district_max_lng" class="form-label">الحد الأقصى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="add_district_max_lng" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="saveDistrictBtn" class="btn btn-primary">إضافة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: تعديل منطقة فرعية -->
    <div class="modal fade" id="editDistrictModal" tabindex="-1" aria-labelledby="editDistrictModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editDistrictModalLabel">تعديل منطقة فرعية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editDistrictForm">
                        <input type="hidden" id="edit_district_id">
                        <input type="hidden" id="edit_district_city_id">
                        <div class="mb-3">
                            <label for="edit_district_name" class="form-label">اسم المنطقة الفرعية</label>
                            <input type="text" class="form-control" id="edit_district_name" required>
                            <div class="form-text">سيتم إضافة اسم المدينة تلقائيًا قبل اسم المنطقة الفرعية (مثال: طرابلس - وسط المدينة)</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_district_lat" class="form-label">خط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_district_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_district_lng" class="form-label">خط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_district_lng" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_district_min_lat" class="form-label">الحد الأدنى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_district_min_lat" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_district_max_lat" class="form-label">الحد الأقصى لخط العرض</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_district_max_lat" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_district_min_lng" class="form-label">الحد الأدنى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_district_min_lng" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_district_max_lng" class="form-label">الحد الأقصى لخط الطول</label>
                                <input type="number" step="0.0001" class="form-control" id="edit_district_max_lng" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="updateDistrictBtn" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: حذف منطقة فرعية -->
    <div class="modal fade" id="deleteDistrictModal" tabindex="-1" aria-labelledby="deleteDistrictModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteDistrictModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    </div>
                    <p>هل أنت متأكد من رغبتك في حذف المنطقة الفرعية <strong id="delete_district_name"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                    <input type="hidden" id="delete_district_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="deleteDistrictBtn" class="btn btn-danger">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: إضافة مجموعة مناطق فرعية -->
    <div class="modal fade" id="addDistrictGroupModal" tabindex="-1" aria-labelledby="addDistrictGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDistrictGroupModalLabel">إضافة مجموعة مناطق فرعية لمدينة <span id="city_name_group_title"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addDistrictGroupForm">
                        <input type="hidden" id="add_district_group_city_id">

                        <div class="mb-3">
                            <label for="district_group_list" class="form-label">أدخل أسماء المناطق الفرعية (كل منطقة في سطر منفصل)</label>
                            <textarea class="form-control" id="district_group_list" rows="10" placeholder="أدخل أسماء المناطق الفرعية هنا، كل منطقة في سطر منفصل
مثال:
وسط المدينة
باب البحر
سوق الجمعة
أبو سليم
عين زارة
تاجوراء"></textarea>
                            <div class="form-text">سيتم إضافة اسم المدينة تلقائيًا قبل اسم كل منطقة فرعية (مثال: طرابلس - وسط المدينة)</div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="use_default_coordinates" checked>
                                <label class="form-check-label" for="use_default_coordinates">
                                    استخدام إحداثيات المدينة الأم للمناطق الفرعية
                                </label>
                                <div class="form-text">إذا تم تحديد هذا الخيار، سيتم استخدام إحداثيات المدينة الأم لجميع المناطق الفرعية</div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="saveDistrictGroupBtn" class="btn btn-success">إضافة المجموعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Leaflet Map Library -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- تحميل بيانات المدن والمناطق من قاعدة البيانات -->
    <script src="{{ url_for('static', filename='js/regions-loader.js') }}"></script>

    <!-- Admin Map Manager -->
    <script src="{{ url_for('static', filename='js/admin-map.js') }}"></script>

    <!-- Admin Lists Manager -->
    <script src="{{ url_for('static', filename='js/admin-lists.js') }}"></script>

    <!-- Admin Regions Manager -->
    <script src="{{ url_for('static', filename='js/admin-regions.js') }}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تفعيل tooltips لعرض معلومات الصلاحيات
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            // تعديل مستخدم
            const editUserBtns = document.querySelectorAll('.edit-user-btn');
            editUserBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const userId = this.getAttribute('data-id');
                    const username = this.getAttribute('data-username');
                    // لم نعد نستخدم البريد الإلكتروني
                    const role = this.getAttribute('data-role');
                    const active = this.getAttribute('data-active');

                    document.getElementById('edit_user_id').value = userId;
                    document.getElementById('edit_username').value = username;
                    document.getElementById('edit_phone').value = this.getAttribute('data-phone');
                    document.getElementById('edit_role').value = role;
                    document.getElementById('edit_is_active').checked = active == '1';
                });
            });

            // حذف مستخدم
            const deleteUserBtns = document.querySelectorAll('.delete-user-btn');
            deleteUserBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const userId = this.getAttribute('data-id');
                    const username = this.getAttribute('data-username');

                    document.getElementById('delete_user_id').value = userId;
                    document.getElementById('delete_username').textContent = username;
                });
            });

            // ===== إدارة القوائم المخصصة =====
            // المتغيرات العامة
            let lists = [];
            let marketers = [];
            let assignedMarketers = {};

            // الحصول على جميع القوائم
            function fetchLists() {
                fetch('/api/custom-lists')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            lists = data.lists;
                            renderLists();
                        } else {
                            showAlert('حدث خطأ أثناء تحميل القوائم: ' + data.error, 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching lists:', error);
                        showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                    });
            }

            // الحصول على المسوقين
            function fetchMarketers() {
                // تصفية المستخدمين للحصول على المسوقين فقط (role_id = 2)
                marketers = Array.from(document.querySelectorAll('tbody tr')).filter(row => {
                    const roleCell = row.querySelector('td:nth-child(4)');
                    return roleCell && roleCell.textContent.trim().includes('مسوّق');
                }).map(row => {
                    const userId = row.querySelector('.edit-user-btn').getAttribute('data-id');
                    const username = row.querySelector('td:nth-child(2)').textContent.trim();
                    return { id: userId, username: username };
                });
            }

            // عرض القوائم في الجدول
            function renderLists() {
                const tableBody = document.querySelector('#listsTable tbody');
                tableBody.innerHTML = '';

                if (lists.length === 0) {
                    tableBody.innerHTML = `<tr><td colspan="6" class="text-center">لا توجد قوائم متاحة</td></tr>`;
                    return;
                }

                lists.forEach((list, index) => {
                    const row = document.createElement('tr');

                    // تعطيل حذف القائمة الافتراضية (id = 1)
                    const deleteBtn = list.id === 1 ?
                        `<button type="button" class="btn btn-sm btn-outline-secondary" disabled title="لا يمكن حذف القائمة الافتراضية"><i class="fas fa-trash-alt"></i></button>` :
                        `<button type="button" class="btn btn-sm btn-danger delete-list-btn" data-id="${list.id}" data-name="${list.name}"><i class="fas fa-trash-alt"></i></button>`;

                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${list.name}</td>
                        <td>${list.description || '-'}</td>
                        <td><span class="badge bg-info store-count" data-list-id="${list.id}">جاري التحميل...</span></td>
                        <td><button type="button" class="btn btn-sm btn-info assign-list-btn" data-id="${list.id}" data-name="${list.name}"><i class="fas fa-user-tag"></i> تخصيص</button></td>
                        <td>
                            <button type="button" class="btn btn-sm btn-primary edit-list-btn" data-id="${list.id}" data-name="${list.name}" data-description="${list.description || ''}"><i class="fas fa-edit"></i></button>
                            ${deleteBtn}
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                // تحميل عدد المتاجر لكل قائمة
                loadStoreCounts();

                // إضافة مستمعي الأحداث للأزرار
                addListButtonListeners();
            }

            // تم نقل دالة loadStoreCounts إلى ملف admin-lists.js

            // إضافة مستمعي الأحداث لأزرار القوائم
            function addListButtonListeners() {
                // زر تعديل القائمة
                document.querySelectorAll('.edit-list-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const listId = this.getAttribute('data-id');
                        const listName = this.getAttribute('data-name');
                        const listDescription = this.getAttribute('data-description');

                        document.getElementById('edit_list_id').value = listId;
                        document.getElementById('edit_list_name').value = listName;
                        document.getElementById('edit_list_description').value = listDescription;

                        const editListModal = new bootstrap.Modal(document.getElementById('editListModal'));
                        editListModal.show();
                    });
                });

                // زر حذف القائمة
                document.querySelectorAll('.delete-list-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const listId = this.getAttribute('data-id');
                        const listName = this.getAttribute('data-name');

                        document.getElementById('delete_list_id').value = listId;
                        document.getElementById('delete_list_name').textContent = listName;

                        const deleteListModal = new bootstrap.Modal(document.getElementById('deleteListModal'));
                        deleteListModal.show();
                    });
                });

                // زر تخصيص القائمة
                document.querySelectorAll('.assign-list-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const listId = this.getAttribute('data-id');
                        const listName = this.getAttribute('data-name');

                        document.getElementById('assign_list_id').value = listId;
                        document.getElementById('assign_list_name').value = listName;

                        // تحميل المسوقين
                        fetchMarketers();
                        populateMarketerDropdown();

                        // تحميل المسوقين المخصصين لهذه القائمة
                        loadAssignedMarketers(listId);

                        const assignListModal = new bootstrap.Modal(document.getElementById('assignListModal'));
                        assignListModal.show();
                    });
                });
            }

            // ملء قائمة المسوقين في نافذة التخصيص
            function populateMarketerDropdown() {
                const dropdown = document.getElementById('assign_marketer');
                // الاحتفاظ بالخيار الأول فقط (اختر مسوق...)
                dropdown.innerHTML = '<option value="">اختر مسوق...</option>';

                marketers.forEach(marketer => {
                    const option = document.createElement('option');
                    option.value = marketer.id;
                    option.textContent = marketer.username;
                    dropdown.appendChild(option);
                });
            }

            // تحميل المسوقين المخصصين لقائمة معينة
            function loadAssignedMarketers(listId) {
                // تفريغ قائمة المسوقين المخصصين
                const assignedList = document.getElementById('assigned_marketers_list');
                assignedList.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> جاري التحميل...</div>';

                // تنفيذ استعلام API للحصول على المسوقين المخصصين لهذه القائمة
                fetch(`/api/marketer-lists?list_id=${listId}`)
                    .then(response => response.json())
                    .then(data => {
                        const assignedMarketersList = document.getElementById('assigned_marketers_list');
                        assignedMarketersList.innerHTML = '';

                        if (data.success && data.marketers && data.marketers.length > 0) {
                            // تخزين المسوقين المخصصين في المتغير العام
                            assignedMarketers[listId] = data.marketers;

                            // عرض المسوقين المخصصين
                            data.marketers.forEach(marketer => {
                                const item = document.createElement('div');
                                item.className = 'list-group-item d-flex justify-content-between align-items-center';
                                item.innerHTML = `
                                    <span>${marketer.username}</span>
                                    <button type="button" class="btn btn-sm btn-danger remove-marketer-btn" data-user-id="${marketer.id}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                `;
                                assignedMarketersList.appendChild(item);
                            });

                            // إضافة مستمعي الأحداث لأزرار إزالة التخصيص
                            document.querySelectorAll('.remove-marketer-btn').forEach(btn => {
                                btn.addEventListener('click', function() {
                                    const userId = this.getAttribute('data-user-id');
                                    removeMarketerAssignment(listId, userId);
                                });
                            });
                        } else {
                            // إذا لم يكن هناك مسوقين مخصصين
                            assignedMarketersList.innerHTML = '<div class="text-muted text-center">لا يوجد مسوقين مخصصين لهذه القائمة</div>';
                            // تفريغ المتغير العام
                            assignedMarketers[listId] = [];
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching assigned marketers:', error);
                        const assignedMarketersList = document.getElementById('assigned_marketers_list');
                        assignedMarketersList.innerHTML = '<div class="text-danger text-center">حدث خطأ أثناء تحميل المسوقين المخصصين</div>';
                        // تفريغ المتغير العام
                        assignedMarketers[listId] = [];
                    });
            }

            // إضافة قائمة جديدة
            document.getElementById('addListBtn').addEventListener('click', function() {
                const name = document.getElementById('add_list_name').value.trim();
                const description = document.getElementById('add_list_description').value.trim();

                if (!name) {
                    showAlert('يرجى إدخال اسم القائمة', 'warning');
                    return;
                }

                // إرسال طلب إضافة القائمة
                fetch('/api/custom-lists', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    },
                    body: JSON.stringify({ name, description })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إغلاق النافذة المنبثقة
                        bootstrap.Modal.getInstance(document.getElementById('addListModal')).hide();

                        // إعادة تحميل القوائم
                        fetchLists();

                        // إظهار رسالة نجاح
                        showAlert('تم إضافة القائمة بنجاح', 'success');

                        // إعادة تعيين النموذج
                        document.getElementById('addListForm').reset();
                    } else {
                        showAlert('حدث خطأ أثناء إضافة القائمة: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error adding list:', error);
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
            });

            // تحديث قائمة
            document.getElementById('updateListBtn').addEventListener('click', function() {
                const listId = document.getElementById('edit_list_id').value;
                const name = document.getElementById('edit_list_name').value.trim();
                const description = document.getElementById('edit_list_description').value.trim();

                if (!name) {
                    showAlert('يرجى إدخال اسم القائمة', 'warning');
                    return;
                }

                // إرسال طلب تحديث القائمة
                fetch('/api/custom-lists', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    },
                    body: JSON.stringify({ id: listId, name, description })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إغلاق النافذة المنبثقة
                        bootstrap.Modal.getInstance(document.getElementById('editListModal')).hide();

                        // إعادة تحميل القوائم
                        fetchLists();

                        // إظهار رسالة نجاح
                        showAlert('تم تحديث القائمة بنجاح', 'success');
                    } else {
                        showAlert('حدث خطأ أثناء تحديث القائمة: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error updating list:', error);
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
            });

            // حذف قائمة
            document.getElementById('deleteListBtn').addEventListener('click', function() {
                const listId = document.getElementById('delete_list_id').value;

                // إرسال طلب حذف القائمة
                fetch(`/api/custom-lists?id=${listId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إغلاق النافذة المنبثقة
                        bootstrap.Modal.getInstance(document.getElementById('deleteListModal')).hide();

                        // إعادة تحميل القوائم
                        fetchLists();

                        // إظهار رسالة نجاح
                        showAlert('تم حذف القائمة بنجاح', 'success');
                    } else {
                        showAlert('حدث خطأ أثناء حذف القائمة: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error deleting list:', error);
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
            });

            // تخصيص قائمة لمسوق
            document.getElementById('assignListBtn').addEventListener('click', function() {
                const listId = document.getElementById('assign_list_id').value;
                const userId = document.getElementById('assign_marketer').value;

                if (!userId) {
                    showAlert('يرجى اختيار مسوق', 'warning');
                    return;
                }

                // إرسال طلب تخصيص القائمة للمسوق
                fetch('/api/marketer-lists', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    },
                    body: JSON.stringify({ list_id: listId, user_id: userId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تحميل المسوقين المخصصين
                        loadAssignedMarketers(listId);

                        // إظهار رسالة نجاح
                        showAlert('تم تخصيص القائمة للمسوق بنجاح', 'success');

                        // إعادة تعيين قائمة المسوقين
                        document.getElementById('assign_marketer').value = '';
                    } else {
                        showAlert('حدث خطأ أثناء تخصيص القائمة: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error assigning list:', error);
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
            });

            // إزالة تخصيص قائمة من مسوق
            function removeMarketerAssignment(listId, userId) {
                // إرسال طلب إزالة تخصيص القائمة من المسوق
                fetch(`/api/marketer-lists?list_id=${listId}&user_id=${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تحميل المسوقين المخصصين
                        loadAssignedMarketers(listId);

                        // إظهار رسالة نجاح
                        showAlert('تم إزالة تخصيص القائمة من المسوق بنجاح', 'success');
                    } else {
                        showAlert('حدث خطأ أثناء إزالة تخصيص القائمة: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error removing assignment:', error);
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
            }

            // عرض رسالة تنبيه
            function showAlert(message, type = 'info') {
                const alertContainer = document.createElement('div');
                alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
                alertContainer.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // إضافة التنبيه إلى أعلى الصفحة
                const container = document.querySelector('.container');
                container.insertBefore(alertContainer, container.firstChild);

                // إخفاء التنبيه تلقائيًا بعد 3 ثوانٍ
                setTimeout(() => {
                    alertContainer.classList.remove('show');
                    setTimeout(() => alertContainer.remove(), 300);
                }, 3000);
            }

            // ===== إدارة المتاجر المعلقة =====
            // تحميل المتاجر المعلقة
            function fetchPendingStores() {
                fetch('/api/pending-stores')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            renderPendingStores(data.stores);
                        } else {
                            showAlert('حدث خطأ أثناء تحميل المتاجر المعلقة: ' + data.error, 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching pending stores:', error);
                        showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                    });
            }

            // عرض المتاجر المعلقة في الجدول
            function renderPendingStores(stores) {
                const tableBody = document.querySelector('#pendingStoresTable tbody');
                tableBody.innerHTML = '';

                // تحديث عداد المتاجر المعلقة
                document.getElementById('pendingStoresCount').textContent = stores.length;

                if (stores.length === 0) {
                    tableBody.innerHTML = `<tr><td colspan="7" class="text-center">لا توجد متاجر معلقة</td></tr>`;
                    return;
                }

                stores.forEach((store, index) => {
                    const row = document.createElement('tr');
                    const createdAt = new Date(store.created_at).toLocaleString('ar-SA');

                    // التحقق من وجود إحداثيات
                    const hasLocation = store.latitude && store.longitude;
                    const locationCell = hasLocation ?
                        `<button type="button" class="btn btn-sm btn-info view-location-btn" data-store='${JSON.stringify(store)}'>
                            <i class="fas fa-map-marker-alt"></i> عرض الموقع
                        </button>` :
                        `<span class="badge bg-warning text-dark">
                            <i class="fas fa-exclamation-triangle"></i> غير محدد
                        </span>`;

                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${store.name}</td>
                        <td>${store.phone || '-'}</td>
                        <td>${store.marketer_name}</td>
                        <td>${locationCell}</td>
                        <td>${createdAt}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-success approve-store-btn" data-id="${store.id}" data-name="${store.name}">
                                <i class="fas fa-check"></i> موافقة
                            </button>
                            <button type="button" class="btn btn-sm btn-danger reject-store-btn" data-id="${store.id}" data-name="${store.name}">
                                <i class="fas fa-times"></i> رفض
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                // إضافة مستمعي الأحداث للأزرار
                addPendingStoreButtonListeners();

                // إضافة مستمعي الأحداث لأزرار عرض الموقع
                document.querySelectorAll('.view-location-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const store = JSON.parse(this.getAttribute('data-store'));
                        if (adminMapManager) {
                            adminMapManager.showStoreLocation(store);
                        }
                    });
                });
            }

            // إضافة مستمعي الأحداث لأزرار المتاجر المعلقة
            function addPendingStoreButtonListeners() {
                // زر الموافقة على المتجر
                document.querySelectorAll('.approve-store-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const storeId = this.getAttribute('data-id');
                        const storeName = this.getAttribute('data-name');

                        // الحصول على تفاصيل المتجر المعلق
                        fetch(`/api/pending-stores?id=${storeId}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const store = data.store;

                                    // ملء بيانات المتجر في النافذة المنبثقة
                                    document.getElementById('approve_store_id').value = store.id;
                                    document.getElementById('approve_store_name').textContent = store.name;
                                    document.getElementById('approve_store_phone').textContent = store.phone || '-';
                                    document.getElementById('approve_store_date').textContent = new Date(store.created_at).toLocaleString('ar-SA');
                                    document.getElementById('approve_marketer_name').textContent = store.marketer_name;
                                    document.getElementById('approve_marketer_phone').textContent = store.marketer_phone || '-';

                                    // ملء قائمة القوائم المتاحة
                                    const listSelect = document.getElementById('approve_list_id');
                                    listSelect.innerHTML = '';
                                    lists.forEach(list => {
                                        const option = document.createElement('option');
                                        option.value = list.id;
                                        option.textContent = list.name;
                                        // تحديد القائمة المقترحة من المسوق
                                        if (list.id === store.list_id) {
                                            option.selected = true;
                                        }
                                        listSelect.appendChild(option);
                                    });

                                    // عرض النافذة المنبثقة
                                    const approveStoreModal = new bootstrap.Modal(document.getElementById('approveStoreModal'));
                                    approveStoreModal.show();
                                } else {
                                    showAlert('حدث خطأ أثناء تحميل بيانات المتجر: ' + data.error, 'danger');
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching store details:', error);
                                showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                            });
                    });
                });

                // زر رفض المتجر
                document.querySelectorAll('.reject-store-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const storeId = this.getAttribute('data-id');
                        const storeName = this.getAttribute('data-name');

                        document.getElementById('reject_store_id').value = storeId;
                        document.getElementById('reject_store_name').textContent = storeName;

                        const rejectStoreModal = new bootstrap.Modal(document.getElementById('rejectStoreModal'));
                        rejectStoreModal.show();
                    });
                });
            }

            // الموافقة على متجر معلق
            document.getElementById('approveStoreBtn').addEventListener('click', function() {
                const storeId = document.getElementById('approve_store_id').value;
                const listId = document.getElementById('approve_list_id').value;

                // إرسال طلب الموافقة على المتجر
                fetch('/api/pending-stores', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    },
                    body: JSON.stringify({ store_id: storeId, list_id: listId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إغلاق النافذة المنبثقة
                        bootstrap.Modal.getInstance(document.getElementById('approveStoreModal')).hide();

                        // إعادة تحميل المتاجر المعلقة
                        fetchPendingStores();

                        // إظهار رسالة نجاح
                        showAlert('تمت الموافقة على المتجر وإضافته إلى القائمة بنجاح', 'success');
                    } else {
                        showAlert('حدث خطأ أثناء الموافقة على المتجر: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error approving store:', error);
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
            });

            // رفض متجر معلق
            document.getElementById('rejectStoreBtn').addEventListener('click', function() {
                const storeId = document.getElementById('reject_store_id').value;

                // إرسال طلب رفض المتجر
                fetch(`/api/pending-stores?id=${storeId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إغلاق النافذة المنبثقة
                        bootstrap.Modal.getInstance(document.getElementById('rejectStoreModal')).hide();

                        // إعادة تحميل المتاجر المعلقة
                        fetchPendingStores();

                        // إظهار رسالة نجاح
                        showAlert('تم رفض المتجر بنجاح', 'success');
                    } else {
                        showAlert('حدث خطأ أثناء رفض المتجر: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error rejecting store:', error);
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                });
            });

            // تحميل القوائم والمتاجر المعلقة عند تحميل الصفحة
            fetchLists();
            fetchPendingStores();
        });
    </script>

    <!-- Modal: الموافقة على متجر معلق -->
    <div class="modal fade" id="approveStoreModal" tabindex="-1" aria-labelledby="approveStoreModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="approveStoreModalLabel">الموافقة على متجر</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0">بيانات المتجر</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">اسم المتجر:</label>
                                        <div id="approve_store_name" class="form-control-plaintext"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">رقم الهاتف:</label>
                                        <div id="approve_store_phone" class="form-control-plaintext"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">تاريخ الإضافة:</label>
                                        <div id="approve_store_date" class="form-control-plaintext"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0">بيانات المسوق</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">اسم المسوق:</label>
                                        <div id="approve_marketer_name" class="form-control-plaintext"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">رقم الهاتف:</label>
                                        <div id="approve_marketer_phone" class="form-control-plaintext"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0">اختيار القائمة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="approve_list_id" class="form-label">اختر القائمة التي سيتم إضافة المتجر إليها:</label>
                                        <select class="form-select" id="approve_list_id" required>
                                            <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="approve_store_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="approveStoreBtn" class="btn btn-success">موافقة وإضافة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: رفض متجر معلق -->
    <div class="modal fade" id="rejectStoreModal" tabindex="-1" aria-labelledby="rejectStoreModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="rejectStoreModalLabel">رفض متجر</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    </div>
                    <p>هل أنت متأكد من رغبتك في رفض المتجر <strong id="reject_store_name"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                    <input type="hidden" id="reject_store_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="rejectStoreBtn" class="btn btn-danger">رفض</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: عرض المتاجر للنقل -->
    <div class="modal fade" id="storesTransferModal" tabindex="-1" aria-labelledby="storesTransferModalLabel" aria-hidden="true" data-bs-focus="false">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="storesTransferModalLabel">
                        <i class="fas fa-exchange-alt me-2"></i>نقل المتاجر بين المسوقين
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-store fa-2x mb-2"></i>
                                    <h4 id="totalStoresCount">0</h4>
                                    <small>إجمالي المتاجر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <h4 id="totalMarketersCount">0</h4>
                                    <small>المسوقين النشطين</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-square fa-2x mb-2"></i>
                                    <h4 id="selectedStoresCount">0</h4>
                                    <small>المتاجر المحددة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <i class="fas fa-filter fa-2x mb-2"></i>
                                    <h4 id="filteredStoresCount">0</h4>
                                    <small>المتاجر المعروضة</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- شريط البحث والأدوات -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="storeSearchInput" placeholder="البحث بالاسم، المدينة، أو رقم الهاتف...">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="loadStoresForTransfer()">
                                    <i class="fas fa-sync me-1"></i> تحديث
                                </button>
                                <button class="btn btn-outline-success btn-sm" id="selectAllStoresBtn">
                                    <i class="fas fa-check-square me-1"></i> تحديد الكل
                                </button>
                                <button class="btn btn-outline-danger btn-sm" id="deselectAllStoresBtn">
                                    <i class="fas fa-square me-1"></i> إلغاء التحديد
                                </button>
                                <button class="btn btn-primary btn-sm" id="bulkTransferBtn" disabled>
                                    <i class="fas fa-exchange-alt me-1"></i> نقل جماعي
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المتاجر مع شريط تمرير -->
                    <div style="max-height: 500px; overflow-y: auto;" class="border rounded">
                        <table class="table table-hover mb-0" id="storesTransferTable">
                            <thead class="table-dark sticky-top">
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                                    </th>
                                    <th style="width: 40px;">#</th>
                                    <th style="width: 180px;">اسم المتجر</th>
                                    <th style="width: 120px;">رقم الهاتف</th>
                                    <th style="width: 150px;">المسوق الحالي</th>
                                    <th style="width: 130px;">القائمة الحالية</th>
                                    <th style="width: 180px;">المدينة والمنطقة</th>
                                    <th style="width: 100px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: نقل متجر إلى مسوق آخر -->
    <div class="modal fade" id="transferStoreModal" tabindex="-1" aria-labelledby="transferStoreModalLabel" aria-hidden="true" data-bs-focus="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="transferStoreModalLabel">نقل متجر إلى مسوق آخر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label"><strong>المتجر:</strong></label>
                        <p id="transfer_store_name" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><strong>المسوق الحالي:</strong></label>
                        <p id="transfer_current_marketer" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label for="transfer_target_marketer" class="form-label">المسوق الجديد:</label>
                        <select class="form-select" id="transfer_target_marketer" required>
                            <option value="">اختر مسوق...</option>
                            <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم نقل المتجر إلى أول قائمة مخصصة للمسوق الجديد، أو إلى القائمة الافتراضية إذا لم يكن له قوائم مخصصة.
                    </div>
                    <input type="hidden" id="transfer_store_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="transferStoreBtn" class="btn btn-primary">نقل المتجر</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: النقل الجماعي للمتاجر -->
    <div class="modal fade" id="bulkTransferModal" tabindex="-1" aria-labelledby="bulkTransferModalLabel" aria-hidden="true" data-bs-focus="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="bulkTransferModalLabel">
                        <i class="fas fa-exchange-alt me-2"></i>نقل جماعي للمتاجر
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم نقل <strong id="bulkTransferCount">0</strong> متجر إلى المسوق المحدد.
                    </div>

                    <div class="mb-3">
                        <label for="bulk_target_marketer" class="form-label">اختر المسوق الجديد:</label>
                        <select class="form-select" id="bulk_target_marketer" required>
                            <option value="">اختر مسوق...</option>
                            <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المتاجر المحددة للنقل:</label>
                        <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto; background-color: #1c2333; border-color: #495057;">
                            <div id="selectedStoresList">
                                <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        سيتم نقل جميع المتاجر المحددة إلى أول قائمة مخصصة للمسوق الجديد، أو إلى القائمة الافتراضية إذا لم يكن له قوائم مخصصة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="confirmBulkTransferBtn" class="btn btn-primary">
                        <i class="fas fa-exchange-alt me-1"></i>تأكيد النقل الجماعي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
