# مثال على معالج الأخطاء في Flask
from flask import Flask, jsonify
from flask_login import current_user

app = Flask(__name__)

def log_error(app, error, user_id=None):
    """دالة لتسجيل الأخطاء"""
    app.logger.error(f"خطأ: {str(error)}, المستخدم: {user_id}")

@app.route('/api/example')
def example_endpoint():
    """مثال على endpoint مع معالجة الأخطاء"""
    try:
        # كود التطبيق هنا
        result = {"message": "نجح العمل", "success": True}
        return jsonify(result), 200

    except Exception as e:
        # معالجة الأخطاء
        user_id = current_user.id if current_user.is_authenticated else None
        log_error(app, e, user_id=user_id)
        return jsonify({'error': str(e), 'success': False}), 500

if __name__ == '__main__':
    app.run(debug=True)