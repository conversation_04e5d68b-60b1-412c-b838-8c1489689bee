import os
from flask import Flask, render_template, request, jsonify, send_from_directory, redirect, url_for, flash, session
from flask_login import Login<PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_wtf.csrf import CSRFProtect
from werkzeug.security import check_password_hash, generate_password_hash
from werkzeug.utils import secure_filename
from db import close_db, init_db, get_db
from models import Store, User, Role, CustomList, ValidationError, PendingStore, StoreStatistics
from datetime import datetime
import json
from forms import LoginForm, RegisterForm, UserForm
from auth import admin_required
from config import config
from logger import log_info, log_error, log_security

# إعداد التطبيق
app = Flask(__name__)
app.config.from_object(config['development'])
config['development'].init_app(app)

# إعداد حماية CSRF
csrf = CSRFProtect(app)

# إعداد مدير تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'  # type: ignore



# التأكد من وجود مجلد التحميلات
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

def allowed_file(filename):
    # التحقق من أن الملف له امتداد مسموح به
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# تسجيل دالة إغلاق قاعدة البيانات
app.teardown_appcontext(close_db)

# تهيئة قاعدة البيانات عند بدء التطبيق
init_db(app)



# إضافة رؤوس API
@app.after_request
def add_api_headers(response):
    """إضافة رؤوس API"""
    if request.path.startswith('/api/'):
        response.headers['Cache-Control'] = 'private, max-age=300'  # 5 دقائق
    return response

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم لنظام تسجيل الدخول"""
    # طباعة معرف المستخدم للتشخيص
    print(f"Loading user with ID: {user_id}")

    user_data = User.get_by_id(user_id)
    if user_data:
        user = User(
            id=user_data['id'],
            username=user_data['username'],
            email=user_data.get('email'),
            phone=user_data.get('phone'),
            role_id=user_data['role_id'],
            is_active=user_data['is_active'] == 1
        )
        return user
    return None

@app.route('/')
def index():
    return render_template('index.html', current_user=current_user)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    # إذا كان المستخدم مسجل الدخول بالفعل
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = LoginForm()
    if form.validate_on_submit():
        username = form.username.data
        password = form.password.data
        remember = form.remember.data

        try:
            # التحقق من المستخدم
            user = User.verify_password(username, password)

            # طباعة معلومات المستخدم للتشخيص
            if user:
                print(f"User verified: id={user['id']}, username={user['username']}")
            else:
                print(f"User verification failed for username: {username}")

            if user and user.get('is_active', 0) == 1:
                # إنشاء كائن UserMixin
                user_obj = User(
                    id=user['id'],
                    username=user['username'],
                    email=user.get('email'),
                    phone=user.get('phone'),
                    role_id=user['role_id']
                )

                # طباعة معلومات المستخدم للتشخيص
                print(f"User object created: id={user_obj.id}, username={user_obj.username}")

                # تسجيل دخول المستخدم وجعل الجلسة دائمة
                login_user(user_obj, remember=remember)
                session.permanent = True

                # إضافة معلومات إضافية للجلسة
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['role_id'] = user['role_id']

                # طباعة معلومات الجلسة للتشخيص
                print(f"Session data: user_id={session.get('user_id')}, username={session.get('username')}")

                # تسجيل نجاح تسجيل الدخول
                log_security(app, "تسجيل دخول", user_id=user['id'], success=True)

                # التوجيه إلى الصفحة المطلوبة أو الرئيسية
                next_page = request.args.get('next')
                return redirect(next_page or url_for('index'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
                # تسجيل فشل تسجيل الدخول
                log_security(app, "محاولة تسجيل دخول فاشلة", user_id=None, success=False)

        except Exception as e:
            flash('حدث خطأ أثناء تسجيل الدخول', 'danger')
            log_error(app, e, additional_info={"username": username})

    return render_template('login.html', form=form)

@app.route('/register', methods=['GET', 'POST'])
def register():
    """صفحة تسجيل مستخدم جديد"""
    # إذا كان المستخدم مسجل الدخول بالفعل
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = RegisterForm()
    if form.validate_on_submit():
        try:
            # إنشاء مستخدم جديد
            user_data = {
                'username': form.username.data,
                'phone': form.phone.data,
                'password': form.password.data,
                'role_id': 3,  # دور الزائر
                'is_active': 1
            }

            user = User.create(user_data)
            user_id = user['id']
            print(f"Created new user with ID: {user_id}, username: {user['username']}")
            flash('تم إنشاء الحساب بنجاح، يمكنك الآن تسجيل الدخول', 'success')
            log_info(app, "تسجيل مستخدم جديد", user_id=user_id)

            # تسجيل دخول المستخدم مباشرة بعد التسجيل
            user_obj = User(
                id=user['id'],
                username=user['username'],
                email=user.get('email'),
                phone=user.get('phone'),
                role_id=user['role_id']
            )

            # طباعة معلومات المستخدم للتشخيص
            print(f"User object created: id={user_obj.id}, username={user_obj.username}")

            login_user(user_obj, remember=True)
            session.permanent = True
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role_id'] = user['role_id']

            # طباعة معلومات الجلسة للتشخيص
            print(f"Session data: user_id={session.get('user_id')}, username={session.get('username')}")

            # تسجيل نجاح تسجيل الدخول
            log_security(app, "تسجيل دخول", user_id=user['id'], success=True)

            return redirect(url_for('index'))

        except ValidationError as e:
            flash(str(e), 'danger')
        except Exception as e:
            flash('حدث خطأ أثناء إنشاء الحساب', 'danger')
            log_error(app, e, additional_info={"form_data": {
                'username': form.username.data,
                'email': form.email.data
            }})

    return render_template('register.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """تسجيل خروج المستخدم"""
    user_id = current_user.id
    logout_user()
    session.clear()
    flash('تم تسجيل خروجك بنجاح', 'success')
    log_security(app, "تسجيل خروج", user_id=user_id, success=True)
    return redirect(url_for('login'))

@app.route('/profile')
@login_required
def profile():
    user = User.get_by_id(current_user.id)
    if not user:
        flash('حدث خطأ في تحميل بيانات المستخدم', 'danger')
        return redirect(url_for('index'))

    return render_template('profile.html', user=user)

@app.route('/profile/update', methods=['POST'])
@login_required
def profile_update():
    user_id = current_user.id
    data = {}

    # الحصول على البيانات من النموذج
    data['username'] = request.form.get('username')
    data['phone'] = request.form.get('phone')

    # التحقق من البيانات المطلوبة
    if not data['username'] or not data['phone']:
        flash('يجب توفير اسم المستخدم ورقم الهاتف', 'danger')
        return redirect(url_for('profile'))

    # تحديث المستخدم
    updated_user = User.update(user_id, data)

    if updated_user:
        flash('تم تحديث الملف الشخصي بنجاح', 'success')
    else:
        flash('حدث خطأ أثناء تحديث الملف الشخصي', 'danger')

    return redirect(url_for('profile'))

@app.route('/change-password', methods=['POST'])
@login_required
def change_password():
    user_id = current_user.id
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    # التحقق من البيانات المطلوبة
    if not current_password or not new_password or not confirm_password:
        flash('يجب توفير جميع الحقول', 'danger')
        return redirect(url_for('profile'))

    # التحقق من تطابق كلمة المرور الجديدة
    if new_password != confirm_password:
        flash('كلمة المرور الجديدة غير متطابقة', 'danger')
        return redirect(url_for('profile'))

    # التحقق من كلمة المرور الحالية
    user = User.get_by_id(user_id)
    if not user:
        flash('حدث خطأ في تحميل بيانات المستخدم', 'danger')
        return redirect(url_for('profile'))

    # التحقق من صحة كلمة المرور الحالية
    if not check_password_hash(user['password_hash'], current_password):
        flash('كلمة المرور الحالية غير صحيحة', 'danger')
        return redirect(url_for('profile'))

    # تحديث كلمة المرور
    data = {'password': new_password}
    updated_user = User.update(user_id, data)

    if updated_user:
        flash('تم تغيير كلمة المرور بنجاح', 'success')
    else:
        flash('حدث خطأ أثناء تغيير كلمة المرور', 'danger')

    return redirect(url_for('profile'))

@app.route('/admin')
@admin_required
def admin_panel():
    """لوحة الإدارة الرئيسية"""
    users = User.get_all()
    roles = Role.get_all()

    # طباعة الأدوار للتشخيص
    print("Available roles:")
    for role in roles:
        print(f"Role ID: {role['id']}, Name: {role['name']}, Description: {role['description']}")

    form = UserForm()
    form.role_id.choices = [(role['id'], role['description']) for role in roles]

    return render_template('admin.html', users=users, roles=roles, form=form)

@app.route('/admin/add-user', methods=['POST'])
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    data = {
        'username': request.form.get('username'),
        'phone': request.form.get('phone'),
        'email': f"{request.form.get('username')}@example.com",  # قيمة افتراضية للبريد الإلكتروني
        'password': request.form.get('password'),
        'role_id': int(request.form.get('role_id', 3)),  # Default to visitor role if None
        'is_active': 1 if request.form.get('is_active') else 0
    }

    # التحقق من البيانات المطلوبة
    if not data['username'] or not data['phone'] or not data['password']:
        flash('يجب توفير اسم المستخدم ورقم الهاتف وكلمة المرور', 'danger')
        return redirect(url_for('admin_panel'))

    try:
        # التحقق من عدم وجود اسم المستخدم أو رقم الهاتف مسبقًا
        existing_username = User.get_by_username(data['username'])
        if existing_username:
            flash('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر', 'danger')
            return redirect(url_for('admin_panel'))

        existing_phone = User.get_by_phone(data['phone'])
        if existing_phone:
            flash('رقم الهاتف موجود بالفعل، يرجى استخدام رقم آخر', 'danger')
            return redirect(url_for('admin_panel'))

        # إنشاء المستخدم
        user = User.create(data)

        if user:
            flash('تم إضافة المستخدم بنجاح', 'success')
            log_info(app, "إضافة مستخدم جديد", user_id=current_user.id, additional_info={"added_user_id": user['id']})
        else:
            flash('حدث خطأ أثناء إضافة المستخدم', 'danger')
            log_error(app, "Error adding user", additional_info={"form_data": data})

    except ValidationError as e:
        flash(str(e), 'danger')
        log_error(app, f"Validation error: {str(e)}", additional_info={"form_data": data})

    except Exception as e:
        flash('حدث خطأ أثناء إضافة المستخدم', 'danger')
        log_error(app, f"Exception: {str(e)}", additional_info={"form_data": data})

    return redirect(url_for('admin_panel'))

@app.route('/admin/edit-user', methods=['POST'])
@admin_required
def edit_user():
    """تعديل مستخدم"""
    user_id = request.form.get('user_id')
    data = {
        'username': request.form.get('username'),
        'phone': request.form.get('phone'),
        'email': f"{request.form.get('username')}@example.com",  # قيمة افتراضية للبريد الإلكتروني
        'role_id': int(request.form.get('role_id', 3)),
        'is_active': 1 if request.form.get('is_active') else 0
    }

    # التحقق من البيانات المطلوبة
    if not user_id or not data['username'] or not data['phone']:
        flash('يجب توفير معرف المستخدم واسم المستخدم ورقم الهاتف', 'danger')
        return redirect(url_for('admin_panel'))

    try:
        # الحصول على المستخدم الحالي
        current_user_data = User.get_by_id(user_id)
        if not current_user_data:
            flash('لم يتم العثور على المستخدم', 'danger')
            return redirect(url_for('admin_panel'))

        # التحقق من عدم وجود اسم المستخدم أو البريد الإلكتروني لمستخدم آخر
        if data['username'] != current_user_data['username']:
            existing_username = User.get_by_username(data['username'])
            if existing_username and existing_username['id'] != user_id:
                flash('اسم المستخدم موجود بالفعل لمستخدم آخر، يرجى اختيار اسم آخر', 'danger')
                return redirect(url_for('admin_panel'))

        if data['phone'] != current_user_data['phone']:
            existing_phone = User.get_by_phone(data['phone'])
            if existing_phone and existing_phone['id'] != user_id:
                flash('رقم الهاتف موجود بالفعل لمستخدم آخر، يرجى استخدام رقم آخر', 'danger')
                return redirect(url_for('admin_panel'))

        # إضافة كلمة المرور إذا تم توفيرها
        password = request.form.get('password')
        if password:
            data['password'] = password

        # تحديث المستخدم
        updated_user = User.update(user_id, data)

        if updated_user:
            flash('تم تحديث المستخدم بنجاح', 'success')
            log_info(app, "تحديث مستخدم", user_id=current_user.id, additional_info={"updated_user_id": user_id})
        else:
            flash('حدث خطأ أثناء تحديث المستخدم', 'danger')
            log_error(app, "Error updating user", additional_info={"user_id": user_id, "form_data": data})

    except ValidationError as e:
        flash(str(e), 'danger')
        log_error(app, f"Validation error: {str(e)}", additional_info={"user_id": user_id, "form_data": data})

    except Exception as e:
        flash('حدث خطأ أثناء تحديث المستخدم', 'danger')
        log_error(app, f"Exception: {str(e)}", additional_info={"user_id": user_id, "form_data": data})

    return redirect(url_for('admin_panel'))

@app.route('/admin/delete-user', methods=['POST'])
@admin_required
def delete_user():
    """حذف مستخدم"""
    user_id = request.form.get('user_id')

    # التحقق من وجود معرف المستخدم
    if not user_id:
        flash('يجب توفير معرف المستخدم', 'danger')
        return redirect(url_for('admin_panel'))

    # التحقق من عدم حذف المستخدم الحالي
    if str(user_id) == str(current_user.id):
        flash('لا يمكنك حذف حسابك الحالي', 'danger')
        return redirect(url_for('admin_panel'))

    try:
        # حذف المستخدم
        success = User.delete(user_id)

        if success:
            flash('تم حذف المستخدم بنجاح', 'success')
            log_info(app, "حذف مستخدم", user_id=current_user.id, additional_info={"deleted_user_id": user_id})
        else:
            flash('حدث خطأ أثناء حذف المستخدم', 'danger')
            log_error(app, "Error deleting user", additional_info={"user_id": user_id})

    except Exception as e:
        flash('حدث خطأ أثناء حذف المستخدم', 'danger')
        log_error(app, e, additional_info={"user_id": user_id})

    return redirect(url_for('admin_panel'))

@app.route('/api/lists', methods=['GET'])
@login_required
def get_lists():
    """الحصول على جميع القوائم المتاحة"""
    try:
        # إذا كان المستخدم مدير، إرجاع جميع القوائم
        if current_user.role_id == Role.ADMIN:
            lists = CustomList.get_all()
        # إذا كان المستخدم مسوق، إرجاع القوائم المخصصة له فقط
        elif current_user.role_id == Role.MARKETER:
            lists = CustomList.get_marketer_lists(current_user.id)
            # إذا لم يكن لدى المسوق أي قوائم مخصصة، إرجاع قائمة فارغة
            if not lists:
                lists = []
        # إذا كان المستخدم زائر، إرجاع جميع القوائم (للعرض فقط)
        else:
            lists = CustomList.get_all()

        return jsonify({'lists': lists, 'success': True})
    except Exception as e:
        log_error(app, e, user_id=current_user.id)
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/custom-lists', methods=['GET', 'POST', 'PUT', 'DELETE'])
@login_required
def manage_custom_lists():
    """إدارة القوائم المخصصة"""
    # الحصول على جميع القوائم
    if request.method == 'GET':
        try:
            # إذا كان المستخدم مدير، إرجاع جميع القوائم
            if current_user.role_id == Role.ADMIN:
                lists = CustomList.get_all()
            # إذا كان المستخدم مسوق، إرجاع القوائم المخصصة له فقط
            elif current_user.role_id == Role.MARKETER:
                lists = CustomList.get_marketer_lists(current_user.id)
                # إذا لم يكن لدى المسوق أي قوائم مخصصة، إرجاع قائمة فارغة
                if not lists:
                    lists = []
            # إذا كان المستخدم زائر، إرجاع جميع القوائم (للعرض فقط)
            else:
                lists = CustomList.get_all()

            return jsonify({'lists': lists, 'success': True})
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # إضافة قائمة جديدة
    elif request.method == 'POST':
        # مطلوب دور مدير
        if current_user.role_id != Role.ADMIN:
            return jsonify({'error': 'ليس لديك صلاحية لإضافة قوائم', 'success': False}), 403

        try:
            # الحصول على البيانات من الطلب
            data = request.get_json()
            if not data:
                return jsonify({'error': 'البيانات غير صالحة', 'success': False}), 400

            # التحقق من وجود البيانات المطلوبة
            if not data.get('name'):
                return jsonify({'error': 'اسم القائمة مطلوب', 'success': False}), 400

            # إنشاء القائمة
            list_data = {
                'name': data.get('name'),
                'description': data.get('description', '')
            }
            new_list = CustomList.create(list_data)

            return jsonify({'list': new_list, 'success': True})
        except ValidationError as e:
            return jsonify({'error': str(e), 'success': False}), 400
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # تحديث قائمة
    elif request.method == 'PUT':
        # مطلوب دور مدير
        if current_user.role_id != Role.ADMIN:
            return jsonify({'error': 'ليس لديك صلاحية لتعديل القوائم', 'success': False}), 403

        try:
            # الحصول على البيانات من الطلب
            data = request.get_json()
            if not data:
                return jsonify({'error': 'البيانات غير صالحة', 'success': False}), 400

            # التحقق من وجود البيانات المطلوبة
            if not data.get('id') or not data.get('name'):
                return jsonify({'error': 'معرف القائمة واسمها مطلوبان', 'success': False}), 400

            # تحديث القائمة
            list_data = {
                'name': data.get('name'),
                'description': data.get('description', '')
            }
            updated_list = CustomList.update(data.get('id'), list_data)

            if updated_list:
                return jsonify({'list': updated_list, 'success': True})
            else:
                return jsonify({'error': 'لم يتم العثور على القائمة', 'success': False}), 404
        except ValidationError as e:
            return jsonify({'error': str(e), 'success': False}), 400
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # حذف قائمة
    elif request.method == 'DELETE':
        # مطلوب دور مدير
        if current_user.role_id != Role.ADMIN:
            return jsonify({'error': 'ليس لديك صلاحية لحذف القوائم', 'success': False}), 403

        try:
            # الحصول على معرف القائمة من الاستعلام
            list_id = request.args.get('id')
            if not list_id:
                return jsonify({'error': 'معرف القائمة مطلوب', 'success': False}), 400

            # التحقق من عدم حذف القائمة الافتراضية (id = 1)
            if int(list_id) == 1:
                return jsonify({'error': 'لا يمكن حذف القائمة الافتراضية', 'success': False}), 400

            # حذف القائمة
            success = CustomList.delete(list_id)

            if success:
                return jsonify({'success': True})
            else:
                return jsonify({'error': 'لم يتم العثور على القائمة', 'success': False}), 404
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/mobile/lists', methods=['GET'])
def mobile_lists():
    """مسار خاص للأجهزة المحمولة للحصول على القوائم بدون تسجيل دخول"""
    try:
        # الحصول على معرف المستخدم من الاستعلام
        user_id = request.args.get('user_id')

        # إذا تم توفير معرف المستخدم وكان المستخدم مسوق، الحصول على القوائم المخصصة له
        if user_id:
            # الحصول على المستخدم
            user = User.get_by_id(user_id)
            if user and user['role_id'] == Role.MARKETER:
                # الحصول على القوائم المخصصة للمسوق
                lists = CustomList.get_marketer_lists(user_id)
                # إذا لم يكن لدى المسوق أي قوائم مخصصة، إرجاع قائمة فارغة
                if not lists:
                    lists = []
            else:
                # إذا لم يكن المستخدم مسوق، إرجاع جميع القوائم
                lists = CustomList.get_all()
        else:
            # إذا لم يتم توفير معرف المستخدم، إرجاع جميع القوائم
            lists = CustomList.get_all()

        # إضافة معلومات للاستجابة
        response = jsonify({
            'lists': lists,
            'success': True
        })

        # إضافة معلومات الجلسة للاستجابة
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/mobile/stores', methods=['GET'])
def mobile_stores():
    """مسار خاص للأجهزة المحمولة للحصول على المتاجر بدون تسجيل دخول"""
    try:
        # الحصول على معرف المستخدم ومعرف القائمة من الاستعلام
        user_id = request.args.get('user_id')
        list_id = request.args.get('list_id')

        # إذا تم توفير معرف المستخدم وكان المستخدم مسوق
        if user_id:
            # الحصول على المستخدم
            user = User.get_by_id(user_id)
            if user and user['role_id'] == Role.MARKETER:
                # الحصول على القوائم المخصصة للمسوق
                marketer_lists = CustomList.get_marketer_lists(user_id)
                marketer_list_ids = [l['id'] for l in marketer_lists]

                # إذا تم تحديد قائمة محددة
                if list_id:
                    try:
                        list_id = int(list_id)
                        # تحقق من أن القائمة مخصصة للمسوق
                        if list_id not in marketer_list_ids:
                            return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه القائمة', 'success': False}), 403

                        # الحصول على المتاجر في هذه القائمة
                        stores = Store.get_all(list_id)
                    except ValueError:
                        return jsonify({'error': 'رقم القائمة غير صالح', 'success': False}), 400
                else:
                    # إذا لم يتم تحديد قائمة، الحصول على المتاجر من جميع القوائم المخصصة للمسوق
                    if marketer_list_ids:
                        # الحصول على المتاجر من جميع القوائم المخصصة للمسوق
                        stores = []
                        for list_id in marketer_list_ids:
                            stores.extend(Store.get_all(list_id))
                    else:
                        # إذا لم يكن لدى المسوق أي قوائم مخصصة، إرجاع قائمة فارغة
                        stores = []
            else:
                # إذا لم يكن المستخدم مسوق، إرجاع جميع المتاجر
                if list_id:
                    try:
                        list_id = int(list_id)
                        stores = Store.get_all(list_id)
                    except ValueError:
                        return jsonify({'error': 'رقم القائمة غير صالح', 'success': False}), 400
                else:
                    stores = Store.get_all()
        else:
            # إذا لم يتم توفير معرف المستخدم، إرجاع جميع المتاجر
            if list_id:
                try:
                    list_id = int(list_id)
                    stores = Store.get_all(list_id)
                except ValueError:
                    return jsonify({'error': 'رقم القائمة غير صالح', 'success': False}), 400
            else:
                stores = Store.get_all()

        # إضافة معلومات للاستجابة
        response = jsonify({
            'stores': stores,
            'success': True
        })

        # إضافة معلومات الجلسة للاستجابة
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/stores', methods=['GET', 'POST', 'PUT', 'DELETE'])
def manage_stores():
    """إدارة المتاجر"""
    if request.method == 'GET':
        try:
            # الحصول على معرف القائمة من الاستعلام
            list_id = request.args.get('list_id')

            # إذا كان المستخدم مسوق، تحقق من أن القائمة مخصصة له
            if current_user.is_authenticated and current_user.role_id == Role.MARKETER:
                # الحصول على القوائم المخصصة للمسوق
                marketer_lists = CustomList.get_marketer_lists(current_user.id)
                marketer_list_ids = [l['id'] for l in marketer_lists]

                # إذا تم تحديد قائمة محددة
                if list_id is not None:
                    list_id = int(list_id)
                    # تحقق من أن القائمة مخصصة للمسوق
                    if list_id not in marketer_list_ids:
                        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه القائمة', 'success': False}), 403

                    # الحصول على المتاجر في هذه القائمة
                    stores = Store.get_all(list_id)
                else:
                    # إذا لم يتم تحديد قائمة، الحصول على المتاجر من جميع القوائم المخصصة للمسوق
                    if marketer_list_ids:
                        # الحصول على المتاجر من جميع القوائم المخصصة للمسوق
                        stores = []
                        for list_id in marketer_list_ids:
                            stores.extend(Store.get_all(list_id))
                    else:
                        # إذا لم يكن لدى المسوق أي قوائم مخصصة، إرجاع قائمة فارغة
                        stores = []
            else:
                # إذا كان المستخدم مدير أو زائر، الحصول على جميع المتاجر
                # تحويل معرف القائمة إلى رقم إذا تم تمريره
                if list_id is not None:
                    list_id = int(list_id)

                # الحصول على المتاجر
                stores = Store.get_all(list_id)

            # طباعة المتاجر للتشخيص
            print("Stores retrieved:", len(stores))

            # التحقق من وجود حقل العنوان والنوع في المتاجر
            stores_with_address = sum(1 for store in stores if store.get('address'))
            stores_with_type = sum(1 for store in stores if store.get('type'))
            print(f"Stores with address field: {stores_with_address} out of {len(stores)}")
            print(f"Stores with type field: {stores_with_type} out of {len(stores)}")

            # طباعة عينة من المتاجر للتشخيص
            if stores:
                print("Sample store data:", stores[0])

                # طباعة جميع المفاتيح الموجودة في بيانات المتجر
                print("Store keys:", list(stores[0].keys()))

                # التحقق من وجود حقل العنوان في بيانات المتجر
                if 'address' in stores[0]:
                    print(f"Address value: '{stores[0]['address']}'")
                else:
                    print("Address field not found in store data")

                # التحقق من وجود حقل النوع في بيانات المتجر
                if 'type' in stores[0]:
                    print(f"Type value: '{stores[0]['type']}'")
                else:
                    print("Type field not found in store data")

                # إضافة حقل العنوان والنوع إذا لم يكن موجوداً
                for store in stores:
                    if 'address' not in store or not store['address']:
                        store['address'] = 'غير محدد'
                        print(f"Added default address to store {store.get('id', 'unknown')}")

                    if 'type' not in store or not store['type']:
                        store['type'] = 'A'
                        print(f"Added default type to store {store.get('id', 'unknown')}")

                # طباعة عينة من المتاجر بعد التعديل
                print("Sample store data after modification:", stores[0])

            # إرجاع المتاجر مغلفة في كائن JSON
            return jsonify(stores)

        except Exception as e:
            log_error(app, e, user_id=current_user.id if current_user.is_authenticated else None)
            return jsonify({'error': str(e)}), 500

    elif request.method == 'POST':
        # مطلوب دور مسوق أو مدير
        if current_user.role_id not in [1, 2]:
            return jsonify({'error': 'ليس لديك صلاحية لإضافة متاجر', 'success': False}), 403

        try:
            # تسجيل البيانات المستلمة للتشخيص
            print("Form data:", request.form)
            print("Files:", request.files)

            # التحقق من وجود البيانات المطلوبة
            if not request.form.get('name'):
                return jsonify({'error': 'اسم المتجر مطلوب', 'success': False}), 400

            if not request.form.get('latitude') or not request.form.get('longitude'):
                return jsonify({'error': 'موقع المتجر مطلوب', 'success': False}), 400

            if not request.form.get('full_address'):
                return jsonify({'error': 'عنوان المتجر مطلوب', 'success': False}), 400

            # الحصول على البيانات
            data = {
                'name': request.form.get('name'),
                'phone': request.form.get('phone', ''),
                'latitude': request.form.get('latitude'),
                'longitude': request.form.get('longitude'),
                'list_id': request.form.get('list_id', 1),
                'type': request.form.get('type', 'A'),
                'address': request.form.get('address', ''),
                'full_address': request.form.get('full_address', ''),
                'city_name': request.form.get('city_name', ''),
                'region_name': request.form.get('region_name', ''),
                'city_id': request.form.get('city_id'),
                'region_id': request.form.get('region_id')
            }

            print("Processed data:", data)

            # معالجة الصورة
            if 'image' in request.files:
                image = request.files['image']
                if image.filename != '':
                    if allowed_file(image.filename):
                        data['image'] = image
                    else:
                        return jsonify({'error': 'نوع الملف غير مسموح به', 'success': False}), 400

            # إذا كان المستخدم مدير، إنشاء المتجر مباشرة
            if current_user.role_id == Role.ADMIN:
                store_id = Store.create(data)
                return jsonify({'store_id': store_id, 'success': True})
            # إذا كان المستخدم مسوق، إنشاء متجر معلق ينتظر موافقة المدير
            else:
                # إضافة معرف المسوق إلى البيانات
                data['marketer_id'] = current_user.id
                pending_store = PendingStore.create(data)
                return jsonify({'pending_store_id': pending_store['id'], 'success': True, 'pending': True, 'message': 'تم إضافة المتجر بنجاح وسيتم مراجعته من قبل المدير'})

        except ValidationError as e:
            print("Validation error:", str(e))
            return jsonify({'error': str(e), 'success': False}), 400
        except Exception as e:
            print("Exception:", str(e))
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    elif request.method == 'PUT':
        # مطلوب دور مسوق أو مدير
        if current_user.role_id not in [1, 2]:
            return jsonify({'error': '', 'success': False}), 403

        try:
            # الحصول على البيانات
            store_id = request.form.get('id')
            if not store_id:
                return jsonify({'error': 'معرف المتجر مطلوب', 'success': False}), 400

            data = {
                'name': request.form.get('name'),
                'phone': request.form.get('phone'),
                'latitude': request.form.get('latitude'),
                'longitude': request.form.get('longitude'),
                'list_id': request.form.get('list_id'),
                'type': request.form.get('type', 'A'),
                'address': request.form.get('address', ''),
                'full_address': request.form.get('full_address', ''),
                'city_name': request.form.get('city_name', ''),
                'region_name': request.form.get('region_name', ''),
                'city_id': request.form.get('city_id'),
                'region_id': request.form.get('region_id')
            }

            # معالجة الصورة
            if 'image' in request.files:
                image = request.files['image']
                if image.filename != '':
                    if allowed_file(image.filename):
                        data['image'] = image
                    else:
                        return jsonify({'error': 'نوع الملف غير مسموح به', 'success': False}), 400

            # تحديث المتجر
            success = Store.update(store_id, data)

            if success:
                return jsonify({'success': True})
            else:
                return jsonify({'error': 'لم يتم العثور على المتجر', 'success': False}), 404

        except ValidationError as e:
            return jsonify({'error': str(e), 'success': False}), 400
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    elif request.method == 'DELETE':
        # مطلوب دور مسوق أو مدير
        if current_user.role_id not in [1, 2]:
            return jsonify({'error': '', 'success': False}), 403

        try:
            # الحصول على معرف المتجر
            store_id = request.args.get('id')
            if not store_id:
                return jsonify({'error': 'معرف المتجر مطلوب', 'success': False}), 400

            # حذف المتجر
            success = Store.delete(store_id)

            if success:
                return jsonify({'success': True})
            else:
                return jsonify({'error': 'لم يتم العثور على المتجر', 'success': False}), 404

        except ValidationError as e:
            return jsonify({'error': str(e), 'success': False}), 400
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

@app.route('/static/uploads/<path:filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/regions/public', methods=['GET'])
def get_public_regions():
    """الحصول على المدن والمناطق للاستخدام العام"""
    db = get_db()
    cursor = db.cursor()

    try:
        # الحصول على معلمات الاستعلام
        region_type = request.args.get('type')
        parent_id = request.args.get('parent_id')

        # إذا تم توفير نوع المنطقة (رئيسية أو فرعية)
        if region_type:
            cursor.execute('''
            SELECT
                r.id, r.name, r.parent_id, r.region_type,
                r.latitude, r.longitude,
                r.min_lat, r.max_lat, r.min_lng, r.max_lng,
                r.created_at, r.updated_at,
                (SELECT COUNT(*) FROM regions WHERE parent_id = r.id) as districts_count
            FROM regions r
            WHERE r.region_type = ?
            ORDER BY r.name
            ''', (region_type,))

            regions = [dict(region) for region in cursor.fetchall()]
            return jsonify({'regions': regions, 'success': True})

        # إذا تم توفير معرف المنطقة الأم (للحصول على المناطق الفرعية)
        elif parent_id:
            cursor.execute('''
            SELECT
                id, name, parent_id, region_type,
                latitude, longitude,
                min_lat, max_lat, min_lng, max_lng,
                created_at, updated_at
            FROM regions
            WHERE parent_id = ?
            ORDER BY name
            ''', (parent_id,))

            regions = [dict(region) for region in cursor.fetchall()]
            return jsonify({'regions': regions, 'success': True})

        # إذا لم يتم توفير أي معلمات، إرجاع جميع المناطق
        else:
            cursor.execute('''
            SELECT
                id, name, parent_id, region_type,
                latitude, longitude,
                min_lat, max_lat, min_lng, max_lng,
                created_at, updated_at
            FROM regions
            ORDER BY region_type, name
            ''')

            regions = [dict(region) for region in cursor.fetchall()]
            return jsonify({'regions': regions, 'success': True})

    except Exception as e:
        user_id = current_user.id if current_user.is_authenticated else None
        log_error(app, e, user_id=user_id)
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/regions', methods=['GET', 'POST', 'PUT', 'DELETE'])
@admin_required
def manage_regions():
    """إدارة المدن والمناطق"""
    db = get_db()
    cursor = db.cursor()

    # الحصول على المدن والمناطق
    if request.method == 'GET':
        try:
            # الحصول على معلمات الاستعلام
            region_type = request.args.get('type')
            parent_id = request.args.get('parent_id')
            region_id = request.args.get('id')

            # إذا تم توفير معرف منطقة محدد
            if region_id:
                cursor.execute('''
                SELECT
                    id, name, parent_id, region_type,
                    latitude, longitude,
                    min_lat, max_lat, min_lng, max_lng,
                    created_at, updated_at
                FROM regions
                WHERE id = ?
                ''', (region_id,))

                region = cursor.fetchone()
                if region:
                    return jsonify({'region': dict(region), 'success': True})
                else:
                    return jsonify({'error': 'لم يتم العثور على المنطقة', 'success': False}), 404

            # إذا تم توفير نوع المنطقة (رئيسية أو فرعية)
            elif region_type:
                cursor.execute('''
                SELECT
                    r.id, r.name, r.parent_id, r.region_type,
                    r.latitude, r.longitude,
                    r.min_lat, r.max_lat, r.min_lng, r.max_lng,
                    r.created_at, r.updated_at,
                    (SELECT COUNT(*) FROM regions WHERE parent_id = r.id) as districts_count
                FROM regions r
                WHERE r.region_type = ?
                ORDER BY r.name
                ''', (region_type,))

                regions = [dict(region) for region in cursor.fetchall()]
                return jsonify({'regions': regions, 'success': True})

            # إذا تم توفير معرف المنطقة الأم (للحصول على المناطق الفرعية)
            elif parent_id:
                cursor.execute('''
                SELECT
                    id, name, parent_id, region_type,
                    latitude, longitude,
                    min_lat, max_lat, min_lng, max_lng,
                    created_at, updated_at
                FROM regions
                WHERE parent_id = ?
                ORDER BY name
                ''', (parent_id,))

                regions = [dict(region) for region in cursor.fetchall()]
                return jsonify({'regions': regions, 'success': True})

            # إذا لم يتم توفير أي معلمات، إرجاع جميع المناطق
            else:
                cursor.execute('''
                SELECT
                    id, name, parent_id, region_type,
                    latitude, longitude,
                    min_lat, max_lat, min_lng, max_lng,
                    created_at, updated_at
                FROM regions
                ORDER BY region_type, name
                ''')

                regions = [dict(region) for region in cursor.fetchall()]
                return jsonify({'regions': regions, 'success': True})

        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # إضافة منطقة جديدة
    elif request.method == 'POST':
        try:
            # الحصول على بيانات المنطقة من الطلب
            data = request.json

            # التحقق من البيانات المطلوبة
            if not data or not data.get('name') or not data.get('region_type'):
                return jsonify({'error': 'يجب توفير اسم المنطقة ونوعها', 'success': False}), 400

            # إعداد البيانات للإدخال
            name = data.get('name')
            region_type = data.get('region_type')
            parent_id = data.get('parent_id')
            latitude = data.get('latitude', 0)
            longitude = data.get('longitude', 0)
            min_lat = data.get('min_lat', 0)
            max_lat = data.get('max_lat', 0)
            min_lng = data.get('min_lng', 0)
            max_lng = data.get('max_lng', 0)
            now = datetime.now().isoformat()

            # إدخال المنطقة الجديدة
            cursor.execute('''
            INSERT INTO regions (
                name, parent_id, region_type,
                latitude, longitude,
                min_lat, max_lat, min_lng, max_lng,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                name, parent_id, region_type,
                latitude, longitude,
                min_lat, max_lat, min_lng, max_lng,
                now, now
            ))

            db.commit()

            # الحصول على معرف المنطقة المدرجة
            region_id = cursor.lastrowid

            # تسجيل العملية
            log_info(app, f"إضافة منطقة جديدة: {name}", user_id=current_user.id)

            return jsonify({
                'id': region_id,
                'message': 'تم إضافة المنطقة بنجاح',
                'success': True
            })

        except Exception as e:
            db.rollback()
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # تحديث منطقة
    elif request.method == 'PUT':
        try:
            # الحصول على بيانات المنطقة من الطلب
            data = request.json

            # التحقق من البيانات المطلوبة
            if not data or not data.get('id') or not data.get('name') or not data.get('region_type'):
                return jsonify({'error': 'يجب توفير معرف المنطقة واسمها ونوعها', 'success': False}), 400

            # إعداد البيانات للتحديث
            region_id = data.get('id')
            name = data.get('name')
            region_type = data.get('region_type')
            parent_id = data.get('parent_id')
            latitude = data.get('latitude', 0)
            longitude = data.get('longitude', 0)
            min_lat = data.get('min_lat', 0)
            max_lat = data.get('max_lat', 0)
            min_lng = data.get('min_lng', 0)
            max_lng = data.get('max_lng', 0)
            now = datetime.now().isoformat()

            # التحقق من وجود المنطقة
            cursor.execute('SELECT * FROM regions WHERE id = ?', (region_id,))
            region = cursor.fetchone()

            if not region:
                return jsonify({'error': 'لم يتم العثور على المنطقة', 'success': False}), 404

            # تحديث المنطقة
            cursor.execute('''
            UPDATE regions SET
                name = ?, parent_id = ?, region_type = ?,
                latitude = ?, longitude = ?,
                min_lat = ?, max_lat = ?, min_lng = ?, max_lng = ?,
                updated_at = ?
            WHERE id = ?
            ''', (
                name, parent_id, region_type,
                latitude, longitude,
                min_lat, max_lat, min_lng, max_lng,
                now, region_id
            ))

            db.commit()

            # تسجيل العملية
            log_info(app, f"تحديث منطقة: {name}", user_id=current_user.id)

            return jsonify({
                'message': 'تم تحديث المنطقة بنجاح',
                'success': True
            })

        except Exception as e:
            db.rollback()
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # حذف منطقة
    elif request.method == 'DELETE':
        try:
            # الحصول على معرف المنطقة من الاستعلام
            region_id = request.args.get('id')

            # التحقق من وجود معرف المنطقة
            if not region_id:
                return jsonify({'error': 'يجب توفير معرف المنطقة', 'success': False}), 400

            # التحقق من وجود المنطقة
            cursor.execute('SELECT * FROM regions WHERE id = ?', (region_id,))
            region = cursor.fetchone()

            if not region:
                return jsonify({'error': 'لم يتم العثور على المنطقة', 'success': False}), 404

            # حذف المناطق الفرعية إذا كانت المنطقة رئيسية
            if region['region_type'] == 'main':
                cursor.execute('DELETE FROM regions WHERE parent_id = ?', (region_id,))

            # حذف المنطقة
            cursor.execute('DELETE FROM regions WHERE id = ?', (region_id,))

            db.commit()

            # تسجيل العملية
            log_info(app, f"حذف منطقة: {region['name']}", user_id=current_user.id)

            return jsonify({
                'message': 'تم حذف المنطقة بنجاح',
                'success': True
            })

        except Exception as e:
            db.rollback()
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500



@app.route('/api/marketer-lists', methods=['GET', 'POST', 'DELETE'])
@admin_required
def manage_marketer_lists():
    """إدارة تخصيص القوائم للمسوقين (مدير فقط)"""
    # الحصول على القوائم المخصصة لمسوق معين أو المسوقين المخصصين لقائمة معينة
    if request.method == 'GET':
        try:
            # الحصول على معرف المستخدم من الاستعلام
            user_id = request.args.get('user_id')
            list_id = request.args.get('list_id')

            # إذا تم توفير معرف المستخدم، الحصول على القوائم المخصصة لهذا المستخدم
            if user_id:
                # الحصول على القوائم المخصصة للمسوق
                lists = CustomList.get_marketer_lists(user_id)
                return jsonify({'lists': lists, 'success': True})

            # إذا تم توفير معرف القائمة، الحصول على المسوقين المخصصين لهذه القائمة
            elif list_id:
                # الحصول على المسوقين المخصصين لهذه القائمة
                marketers = User.get_marketers_for_list(list_id)
                return jsonify({'marketers': marketers, 'success': True})

            # إذا لم يتم توفير أي من المعرفين
            else:
                return jsonify({'error': 'يجب توفير معرف المستخدم أو معرف القائمة', 'success': False}), 400
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # تخصيص قائمة لمسوق
    elif request.method == 'POST':
        try:
            # التحقق من وجود البيانات المطلوبة
            if not request.json or not request.json.get('user_id') or not request.json.get('list_id'):
                return jsonify({'error': 'معرف المستخدم ومعرف القائمة مطلوبان', 'success': False}), 400

            # تخصيص القائمة للمسوق
            user_id = request.json.get('user_id')
            list_id = int(request.json.get('list_id'))

            success = CustomList.assign_to_marketer(list_id, user_id)
            if success:
                return jsonify({'success': True})
            else:
                return jsonify({'error': 'لم يتم العثور على المستخدم أو القائمة أو المستخدم ليس مسوقًا', 'success': False}), 400
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # إزالة تخصيص قائمة من مسوق
    elif request.method == 'DELETE':
        try:
            # التحقق من وجود البيانات المطلوبة
            user_id = request.args.get('user_id')
            list_id = request.args.get('list_id')

            if not user_id or not list_id:
                return jsonify({'error': 'معرف المستخدم ومعرف القائمة مطلوبان', 'success': False}), 400

            # إزالة تخصيص القائمة من المسوق
            success = CustomList.unassign_from_marketer(int(list_id), user_id)
            if success:
                return jsonify({'success': True})
            else:
                return jsonify({'error': 'لم يتم العثور على التخصيص', 'success': False}), 400
        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/transfer-store', methods=['POST'])
@admin_required
@csrf.exempt  # إعفاء من CSRF للطلبات API
def transfer_store():
    """نقل متجر من مسوق إلى مسوق آخر (مدير فقط)"""
    try:
        # التحقق من وجود البيانات المطلوبة
        if not request.json:
            return jsonify({'error': 'لم يتم استلام بيانات JSON', 'success': False}), 400

        store_id = request.json.get('store_id')
        target_marketer_id = request.json.get('target_marketer_id')

        if not store_id or not target_marketer_id:
            return jsonify({'error': 'معرف المتجر ومعرف المسوق الجديد مطلوبان', 'success': False}), 400

        # التحقق من وجود المتجر أولاً
        store = Store.get_by_id(store_id)
        if not store:
            return jsonify({'error': f'لم يتم العثور على المتجر بالمعرف: {store_id}', 'success': False}), 404

        # التحقق من وجود المسوق
        # التأكد من أن معرف المسوق يحتوي على البادئة المطلوبة
        if not target_marketer_id.startswith('user-'):
            target_marketer_id = f"user-{target_marketer_id}"

        target_marketer = User.get_by_id(target_marketer_id)
        if not target_marketer:
            return jsonify({'error': f'لم يتم العثور على المسوق بالمعرف: {target_marketer_id}', 'success': False}), 404

        if target_marketer['role_id'] != Role.MARKETER:
            return jsonify({'error': f'المستخدم {target_marketer["username"]} ليس مسوقاً', 'success': False}), 400

        # نقل المتجر
        success = Store.transfer_to_marketer(store_id, target_marketer_id)

        if success:
            # تسجيل العملية
            log_info(app, f"نقل متجر {store['name']} (ID: {store_id}) إلى المسوق {target_marketer['username']} (ID: {target_marketer_id})", user_id=current_user.id)
            return jsonify({
                'success': True,
                'message': f'تم نقل المتجر "{store["name"]}" إلى المسوق "{target_marketer["username"]}" بنجاح'
            })
        else:
            return jsonify({'error': 'فشل في نقل المتجر. يرجى المحاولة مرة أخرى', 'success': False}), 500

    except Exception as e:
        print(f"Error in transfer_store: {str(e)}")
        log_error(app, e, user_id=current_user.id)
        return jsonify({'error': f'حدث خطأ أثناء نقل المتجر: {str(e)}', 'success': False}), 500

@app.route('/api/bulk-transfer-stores', methods=['POST'])
@admin_required
@csrf.exempt  # إعفاء من CSRF للطلبات API
def bulk_transfer_stores():
    """نقل جماعي للمتاجر إلى مسوق آخر (مدير فقط)"""
    try:
        # التحقق من وجود البيانات المطلوبة
        if not request.json:
            return jsonify({'error': 'لم يتم استلام بيانات JSON', 'success': False}), 400

        store_ids = request.json.get('store_ids', [])
        target_marketer_id = request.json.get('target_marketer_id')

        if not store_ids or not target_marketer_id:
            return jsonify({'error': 'معرفات المتاجر ومعرف المسوق الجديد مطلوبان', 'success': False}), 400

        if not isinstance(store_ids, list) or len(store_ids) == 0:
            return jsonify({'error': 'يجب تحديد متجر واحد على الأقل', 'success': False}), 400

        # التحقق من وجود المسوق
        if not target_marketer_id.startswith('user-'):
            target_marketer_id = f"user-{target_marketer_id}"

        target_marketer = User.get_by_id(target_marketer_id)
        if not target_marketer:
            return jsonify({'error': f'لم يتم العثور على المسوق بالمعرف: {target_marketer_id}', 'success': False}), 404

        if target_marketer['role_id'] != Role.MARKETER:
            return jsonify({'error': f'المستخدم {target_marketer["username"]} ليس مسوقاً', 'success': False}), 400

        # نقل المتاجر واحداً تلو الآخر
        successful_transfers = 0
        failed_transfers = 0
        transferred_stores = []

        for store_id in store_ids:
            # التحقق من وجود المتجر
            store = Store.get_by_id(store_id)
            if not store:
                failed_transfers += 1
                continue

            # نقل المتجر
            success = Store.transfer_to_marketer(store_id, target_marketer_id)
            if success:
                successful_transfers += 1
                transferred_stores.append(store['name'])
            else:
                failed_transfers += 1

        # إعداد رسالة النتيجة
        if successful_transfers > 0:
            message = f'تم نقل {successful_transfers} متجر بنجاح إلى المسوق "{target_marketer["username"]}"'
            if failed_transfers > 0:
                message += f' (فشل في نقل {failed_transfers} متجر)'

            # تسجيل العملية
            log_info(app, f"نقل جماعي: {successful_transfers} متجر إلى المسوق {target_marketer['username']} (ID: {target_marketer_id})", user_id=current_user.id)

            return jsonify({
                'success': True,
                'message': message,
                'successful_transfers': successful_transfers,
                'failed_transfers': failed_transfers,
                'transferred_stores': transferred_stores
            })
        else:
            return jsonify({'error': 'فشل في نقل جميع المتاجر المحددة', 'success': False}), 500

    except Exception as e:
        print(f"Error in bulk_transfer_stores: {str(e)}")
        log_error(app, e, user_id=current_user.id)
        return jsonify({'error': f'حدث خطأ أثناء النقل الجماعي: {str(e)}', 'success': False}), 500

@app.route('/api/marketers', methods=['GET'])
@admin_required
def get_marketers():
    """الحصول على قائمة جميع المسوقين (مدير فقط)"""
    try:
        # الحصول على جميع المسوقين
        marketers = User.get_marketers()
        return jsonify({'marketers': marketers, 'success': True})
    except Exception as e:
        log_error(app, e, user_id=current_user.id)
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/list-marketer/<int:list_id>', methods=['GET'])
@admin_required
def get_list_marketer(list_id):
    """الحصول على المسوق المخصص لقائمة معينة (مدير فقط)"""
    try:
        marketer = CustomList.get_list_marketer(list_id)
        return jsonify({'marketer': marketer, 'success': True})
    except Exception as e:
        log_error(app, e, user_id=current_user.id)
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/pending-stores', methods=['GET', 'POST', 'PUT', 'DELETE'])
@admin_required
def manage_pending_stores():
    """إدارة المتاجر المعلقة (مدير فقط)"""
    # الحصول على جميع المتاجر المعلقة
    if request.method == 'GET':
        try:
            # الحصول على معرف المتجر من الاستعلام
            store_id = request.args.get('id')

            # إذا تم توفير معرف المتجر، الحصول على متجر معين
            if store_id:
                store = PendingStore.get_by_id(store_id)
                if not store:
                    return jsonify({'error': 'لم يتم العثور على المتجر', 'success': False}), 404
                return jsonify({'store': store, 'success': True})

            # الحصول على جميع المتاجر المعلقة
            stores = PendingStore.get_all()
            return jsonify({'stores': stores, 'success': True})

        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # الموافقة على متجر معلق
    elif request.method == 'POST':
        try:
            # التحقق من وجود البيانات المطلوبة
            if not request.json or not request.json.get('store_id') or not request.json.get('list_id'):
                return jsonify({'error': 'معرف المتجر ومعرف القائمة مطلوبان', 'success': False}), 400

            # الموافقة على المتجر
            store_id = request.json.get('store_id')
            list_id = int(request.json.get('list_id'))

            new_store_id = PendingStore.approve(store_id, list_id)
            if new_store_id:
                return jsonify({'store_id': new_store_id, 'success': True, 'message': 'تمت الموافقة على المتجر وإضافته إلى القائمة'})
            else:
                return jsonify({'error': 'لم يتم العثور على المتجر', 'success': False}), 404

        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500

    # رفض متجر معلق
    elif request.method == 'DELETE':
        try:
            # التحقق من وجود معرف المتجر
            store_id = request.args.get('id')
            if not store_id:
                return jsonify({'error': 'معرف المتجر مطلوب', 'success': False}), 400

            # رفض المتجر
            success = PendingStore.reject(store_id)
            if success:
                return jsonify({'success': True, 'message': 'تم رفض المتجر بنجاح'})
            else:
                return jsonify({'error': 'لم يتم العثور على المتجر', 'success': False}), 404

        except Exception as e:
            log_error(app, e, user_id=current_user.id)
            return jsonify({'error': str(e), 'success': False}), 500


@app.route('/statistics')
@login_required
def statistics():
    """صفحة إحصائيات المتاجر حسب المناطق"""
    # التحقق من صلاحيات المستخدم
    if current_user.role_id != Role.ADMIN:
        flash('ليس لديك صلاحية للوصول إلى صفحة الإحصائيات', 'danger')
        return redirect(url_for('index'))

    return render_template('statistics.html')


@app.route('/api/statistics/stores', methods=['GET'])
def get_statistics():
    """الحصول على إحصائيات المتاجر مع إمكانية التصفية"""
    try:
        # استخراج معلمات التصفية من الاستعلام
        filters = {}

        if request.args.get('store_id'):
            filters['store_id'] = request.args.get('store_id')

        if request.args.get('region'):
            filters['region'] = request.args.get('region')

        if request.args.get('store_type'):
            filters['store_type'] = request.args.get('store_type')

        # الحصول على الإحصائيات
        stats = StoreStatistics.get_all(filters)

        return jsonify({
            'success': True,
            'statistics': stats
        })

    except Exception as e:
        log_error(app, e, user_id=current_user.id if current_user.is_authenticated else None)
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/statistics/report', methods=['GET'])
@login_required
def get_statistics_report():
    """إنشاء تقرير إحصائيات المتاجر للطباعة أو التصدير"""
    try:
        # التحقق من صلاحيات المستخدم
        if current_user.role_id != Role.ADMIN:
            return jsonify({'error': 'ليس لديك صلاحية للوصول إلى التقارير', 'success': False}), 403

        # استخراج معلمات التصفية من الاستعلام
        filters = {}

        if request.args.get('region'):
            filters['region'] = request.args.get('region')

        if request.args.get('store_type'):
            filters['store_type'] = request.args.get('store_type')

        # الحصول على جميع المتاجر
        stores = Store.get_all()

        # تطبيق التصفية إذا لزم الأمر باستخدام النظام المحسن
        filtered_stores = stores

        if filters.get('region'):
            filtered_stores = []
            for store in stores:
                # استخراج المدينة الرئيسية والمنطقة من العنوان
                address = store.get('address', '')
                city_name = store.get('city_name', '')
                region_name = store.get('region_name', '')

                # تحديد العنوان الكامل
                full_address = None
                main_city = None

                if city_name and region_name:
                    full_address = f"{city_name} - {region_name}"
                    main_city = city_name
                elif city_name:
                    full_address = city_name
                    main_city = city_name
                elif address:
                    full_address = address
                    if ' - ' in address:
                        main_city = address.split(' - ')[0]
                    else:
                        main_city = address

                # التحقق من تطابق التصفية
                if (main_city == filters['region'] or
                    full_address == filters['region'] or
                    (address and filters['region'] in address)):
                    filtered_stores.append(store)

        if filters.get('store_type'):
            filtered_stores = [store for store in filtered_stores
                             if store.get('type') == filters['store_type']]

        # إنشاء ملخص التقرير
        report_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # تحليل البيانات
        total_stores = len(filtered_stores)
        stores_by_type = {}
        stores_by_region = {}

        for store in filtered_stores:
            # تحليل حسب النوع
            store_type = store.get('type', 'أخرى')
            if store_type not in stores_by_type:
                stores_by_type[store_type] = 0
            stores_by_type[store_type] += 1

            # تحليل حسب المنطقة باستخدام النظام المحسن
            address = store.get('address', '')
            city_name = store.get('city_name', '')
            region_name = store.get('region_name', '')

            # تحديد المدينة الرئيسية
            main_city = 'غير محدد'

            if city_name:
                main_city = city_name
            elif address:
                if ' - ' in address:
                    main_city = address.split(' - ')[0]
                else:
                    main_city = address

            if main_city not in stores_by_region:
                stores_by_region[main_city] = 0
            stores_by_region[main_city] += 1

        # إنشاء التقرير
        report = {
            'success': True,
            'report_date': report_date,
            'filters': filters,
            'summary': {
                'total_stores': total_stores,
                'total_regions': len(stores_by_region),
                'total_types': len(stores_by_type)
            },
            'stores_by_type': stores_by_type,
            'stores_by_region': stores_by_region,
            'stores': filtered_stores
        }

        return jsonify(report)

    except Exception as e:
        log_error(app, e, user_id=current_user.id if current_user.is_authenticated else None)
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/statistics/save-report', methods=['POST'])
@login_required
def save_statistics_report():
    """حفظ تقرير إحصائيات المتاجر"""
    try:
        # التحقق من صلاحيات المستخدم
        if current_user.role_id != Role.ADMIN:
            return jsonify({'error': 'ليس لديك صلاحية لحفظ التقارير', 'success': False}), 403

        data = request.get_json()

        if not data:
            return jsonify({'error': 'لا توجد بيانات للحفظ', 'success': False}), 400

        # يمكن إضافة منطق حفظ التقرير في قاعدة البيانات هنا
        # لكن حالياً سنرجع نجاح العملية فقط

        return jsonify({
            'success': True,
            'message': 'تم حفظ التقرير بنجاح'
        })

    except Exception as e:
        log_error(app, e, user_id=current_user.id if current_user.is_authenticated else None)
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/statistics/cities-districts', methods=['GET'])
@login_required
def get_cities_districts_for_statistics():
    """الحصول على المدن والمناطق الفرعية لاستخدامها في صفحة الإحصائيات"""
    try:
        # التحقق من صلاحيات المستخدم
        if current_user.role_id != Role.ADMIN:
            return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه البيانات', 'success': False}), 403

        # الحصول على جميع المتاجر لاستخراج المدن والمناطق منها
        stores = Store.get_all()

        cities_set = set()
        districts_dict = {}

        for store in stores:
            # استخراج المدينة والمنطقة من المتجر
            address = store.get('address', '')
            city_name = store.get('city_name', '')
            region_name = store.get('region_name', '')

            # تحديد المدينة الرئيسية
            main_city = None
            sub_region = None

            if city_name:
                main_city = city_name
                if region_name:
                    sub_region = region_name
            elif address:
                if ' - ' in address:
                    parts = address.split(' - ')
                    main_city = parts[0].strip()
                    if len(parts) > 1:
                        sub_region = parts[1].strip()
                else:
                    main_city = address.strip()

            # إضافة المدينة الرئيسية
            if main_city and main_city != 'غير محدد':
                cities_set.add(main_city)

                # إنشاء قائمة المناطق الفرعية للمدينة
                if main_city not in districts_dict:
                    districts_dict[main_city] = set()

                # إضافة المنطقة الفرعية
                if sub_region and sub_region != main_city:
                    districts_dict[main_city].add(sub_region)

        # تحويل المجموعات إلى قوائم مرتبة
        cities_list = sorted(list(cities_set))

        # تحويل مجموعات المناطق الفرعية إلى قوائم مرتبة
        for city in districts_dict:
            districts_dict[city] = sorted(list(districts_dict[city]))

        return jsonify({
            'success': True,
            'cities': cities_list,
            'districts': districts_dict
        })

    except Exception as e:
        log_error(app, e, user_id=current_user.id if current_user.is_authenticated else None)
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/user/phone')
@login_required
def get_user_phone():
    """الحصول على رقم هاتف المستخدم الحالي"""
    try:
        # التحقق من وجود المستخدم
        if not current_user or not current_user.is_authenticated:
            return jsonify({'error': '', 'success': False}), 401

        # الحصول على رقم الهاتف
        user_data = User.get_by_id(current_user.id)
        if not user_data:
            return jsonify({'error': 'لم يتم العثور على المستخدم', 'success': False}), 404

        # إرجاع رقم الهاتف
        return jsonify({'phone': user_data.get('phone', ''), 'success': True})

    except Exception as e:
        log_error(app, e, user_id=current_user.id if current_user.is_authenticated else None)
        return jsonify({'error': str(e), 'success': False}), 500

# Route لتجنب خطأ Chrome DevTools
@app.route('/.well-known/appspecific/com.chrome.devtools.json')
def chrome_devtools():
    """Route لتجنب خطأ Chrome DevTools في السجلات"""
    return jsonify({}), 204  # No Content

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)
