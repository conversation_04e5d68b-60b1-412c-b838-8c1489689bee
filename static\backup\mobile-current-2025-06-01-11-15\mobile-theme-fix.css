/**
 * إصلاحات لمشكلة الشاشة السوداء في واجهة الهاتف المحمول - Loacker
 */

/* إصلاحات عامة للألوان والخلفيات */
.mobile-view {
    background-color: #f8f9fa !important;
    color: #333 !important;
}

.mobile-header {
    background-color: white !important;
    color: #333 !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

.mobile-content {
    background-color: #f8f9fa !important;
    color: #333 !important;
}

.mobile-tabs {
    background-color: white !important;
    border-top: 1px solid #eee !important;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1) !important;
}

.mobile-tab-btn {
    background-color: white !important;
    color: #666 !important;
}

.mobile-tab-btn.active {
    color: #d50000 !important;
}

/* إصلاحات للبطاقات والعناصر الأخرى */
.mobile-store-card {
    background-color: white !important;
    color: #333 !important;
}

.mobile-store-card .card-title {
    color: #333 !important;
}

.mobile-store-card .card-subtitle {
    color: #666 !important;
}

.mobile-form .form-label {
    color: #555 !important;
}

.mobile-form .form-control,
.mobile-form .form-select {
    background-color: white !important;
    color: #333 !important;
    border-color: #eee !important;
}

/* إصلاحات للأزرار */
.mobile-form .btn-primary {
    background-color: #d50000 !important;
    border-color: #d50000 !important;
    color: white !important;
}

.mobile-store-card .btn-outline-primary {
    color: #d50000 !important;
    border-color: #d50000 !important;
}

.mobile-store-card .btn-outline-primary:hover,
.mobile-store-card .btn-outline-primary:active {
    background-color: #d50000 !important;
    color: white !important;
}

/* إصلاحات للنصوص والعناوين */
.mobile-logo-wrapper h1 {
    color: #d50000 !important;
}

/* إصلاحات للخريطة */
.mobile-map {
    border: 1px solid #eee !important;
}

/* إصلاحات للنوافذ المنبثقة */
.modal-content {
    background-color: white !important;
    color: #333 !important;
}

.modal-header {
    background-color: #f8f9fa !important;
    color: #333 !important;
}
