<!DOCTYPE html>
<html lang="ar" data-bs-theme="dark" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#212529">
    <title>تسجيل الدخول - Loacker</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Google Fonts - Tajawal & Playfair Display (Classic European Font) -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Playfair+Display:wght@400;700;900&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <style>
        .login-container {
            max-width: 450px;
            margin: 50px auto;
            padding: 20px;
            border-radius: 10px;
            background-color: #2a2a2a;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }

        .logo-circle-small {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
            color: white;
        }

        .text-gradient {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-family: 'Playfair Display', serif;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="text-center mb-4">
                <div class="logo-circle-small">
                    <i class="fas fa-store"></i>
                </div>
                <h1 class="mb-2">
                    <span class="text-gradient">Loacker</span>
                </h1>
                <p class="text-muted">تسجيل الدخول للوصول إلى النظام</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" action="{{ url_for('login') }}">
                {{ form.csrf_token }}
                <div class="mb-3">
                    <label for="username" class="form-label">{{ form.username.label }}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        {{ form.username(class="form-control", placeholder="أدخل اسم المستخدم أو رقم الهاتف") }}
                    </div>
                    {% if form.username.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">{{ form.password.label }}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        {{ form.password(class="form-control", placeholder="أدخل كلمة المرور") }}
                    </div>
                    {% if form.password.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="mb-3 form-check">
                    {{ form.remember(class="form-check-input") }}
                    <label class="form-check-label" for="remember">{{ form.remember.label }}</label>
                </div>

                <div class="d-grid gap-2">
                    {{ form.submit(class="btn btn-gradient btn-lg") }}
                </div>
            </form>

            <div class="mt-3 text-center">
                <p>ليس لديك حساب؟ <a href="{{ url_for('register') }}" class="text-decoration-none">تسجيل حساب جديد</a></p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
