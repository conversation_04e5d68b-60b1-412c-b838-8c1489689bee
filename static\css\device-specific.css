/**
 * أنماط خاصة بالأجهزة المختلفة
 * تم إنشاؤها لدعم مختلف أنواع الهواتف والأجهزة
 */

:root {
    /* متغيرات افتراضية */
    --device-width: 100vw;
    --device-height: 100vh;
    --device-pixel-ratio: 1;
    --device-type: "unknown";
    --device-model: "unknown";
    --safe-area-inset-top: 0px;
    --safe-area-inset-bottom: 0px;
    --font-size-multiplier: 1;
    --device-font-family: "Tajawal", "Cairo", sans-serif;
}

/* أنماط عامة للأجهزة المحمولة */
body.mobile-device {
    font-family: var(--device-font-family);
    font-size: calc(14px * var(--font-size-multiplier));
    padding-top: var(--safe-area-inset-top);
    padding-bottom: var(--safe-area-inset-bottom);
    background-color: #f8f9fa;
}

/* أنماط للهواتف الصغيرة (مثل iPhone 7/8) */
body.mobile-device.small-phone {
    font-size: calc(13px * var(--font-size-multiplier));
}

/* أنماط للهواتف الكبيرة (مثل iPhone 11/12) */
body.mobile-device.large-phone {
    font-size: calc(15px * var(--font-size-multiplier));
}

/* أنماط خاصة بأجهزة iPhone */
body.mobile-device.iphone-device {
    /* أنماط خاصة بأجهزة iPhone */
    --ios-blue: #007aff;
    --ios-red: #ff3b30;
    --ios-green: #34c759;
}

/* أنماط خاصة بأجهزة Xiaomi */
body.mobile-device.xiaomi-device {
    /* أنماط خاصة بأجهزة Xiaomi */
    --miui-blue: #0e87fa;
    --miui-orange: #ff6700;
}

/* أنماط خاصة بأجهزة Samsung */
body.mobile-device.samsung-device {
    /* أنماط خاصة بأجهزة Samsung */
    --samsung-blue: #1428a0;
    --samsung-light-blue: #75b7ff;
}

/* أنماط خاصة بالاتجاه العمودي */
body.portrait-mode .mobile-view {
    max-width: 100vw;
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
}

/* أنماط خاصة بالاتجاه الأفقي */
body.landscape-mode .mobile-view {
    max-width: 100vw;
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
}

/* أنماط واجهة الهاتف المحمول */
.mobile-view {
    display: none;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: #f8f9fa;
    color: #333;
}

/* إظهار واجهة الهاتف المحمول للأجهزة المحمولة */
body.mobile-device .mobile-view {
    display: block !important;
}

/* إخفاء واجهة سطح المكتب للأجهزة المحمولة */
body.mobile-device .container-fluid {
    display: none !important;
}

/* إخفاء الواجهة المؤقتة للهاتف المحمول (Bootstrap) للأجهزة المحمولة الحقيقية */
body.mobile-device .d-md-none {
    display: none !important;
}

/* إظهار الواجهة المؤقتة فقط للشاشات الصغيرة على أجهزة الكمبيوتر */
body:not(.mobile-device) .d-md-none {
    display: block !important;
}

/* أنماط للهواتف الصغيرة */
.mobile-view.small-phone-view {
    /* تعديلات للهواتف الصغيرة */
    font-size: 0.9em;
}

/* أنماط للهواتف الكبيرة */
.mobile-view.large-phone-view {
    /* تعديلات للهواتف الكبيرة */
    font-size: 1em;
}

/* أنماط خاصة بأجهزة iPhone */
.mobile-view.iphone-view {
    /* تعديلات خاصة بأجهزة iPhone */
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", sans-serif;
}

/* أنماط خاصة بأجهزة Xiaomi */
.mobile-view.xiaomi-view {
    /* تعديلات خاصة بأجهزة Xiaomi */
    font-family: "Mi Sans", "Roboto", sans-serif;
}

/* أنماط خاصة بأجهزة Samsung */
.mobile-view.samsung-view {
    /* تعديلات خاصة بأجهزة Samsung */
    font-family: "Samsung Sans", "Roboto", sans-serif;
}

/* تعديلات للأزرار في الهواتف المختلفة */
.mobile-view button, 
.mobile-view .btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
}

/* تعديلات للأزرار في أجهزة iPhone */
.mobile-view.iphone-view button,
.mobile-view.iphone-view .btn {
    border-radius: 10px;
}

/* تعديلات للأزرار في أجهزة Xiaomi */
.mobile-view.xiaomi-view button,
.mobile-view.xiaomi-view .btn {
    border-radius: 6px;
}

/* تعديلات للبطاقات في الهواتف المختلفة */
.mobile-view .card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

/* تعديلات للبطاقات في أجهزة iPhone */
.mobile-view.iphone-view .card {
    border-radius: 14px;
}

/* تعديلات للبطاقات في أجهزة Xiaomi */
.mobile-view.xiaomi-view .card {
    border-radius: 10px;
}

/* تعديلات للخريطة في الهواتف المختلفة */
.mobile-view #mobile-map {
    height: 300px;
    width: 100%;
    border-radius: 12px;
    margin-bottom: 1rem;
}

/* تعديلات للخريطة في الهواتف الصغيرة */
.mobile-view.small-phone-view #mobile-map {
    height: 250px;
}

/* تعديلات للخريطة في الهواتف الكبيرة */
.mobile-view.large-phone-view #mobile-map {
    height: 350px;
}

/* تعديلات للقوائم في الهواتف المختلفة */
.mobile-view .list-group-item {
    padding: 0.75rem 1rem;
    border-radius: 0;
}

/* تعديلات للقوائم في أجهزة iPhone */
.mobile-view.iphone-view .list-group-item {
    padding: 1rem;
}

/* تعديلات للقوائم في أجهزة Xiaomi */
.mobile-view.xiaomi-view .list-group-item {
    padding: 0.875rem 1rem;
}

/* تعديلات للنصوص في الهواتف المختلفة */
.mobile-view h1 {
    font-size: 1.75rem;
}

.mobile-view h2 {
    font-size: 1.5rem;
}

.mobile-view h3 {
    font-size: 1.25rem;
}

.mobile-view p {
    font-size: 1rem;
    line-height: 1.5;
}

/* تعديلات للنصوص في الهواتف الصغيرة */
.mobile-view.small-phone-view h1 {
    font-size: 1.5rem;
}

.mobile-view.small-phone-view h2 {
    font-size: 1.3rem;
}

.mobile-view.small-phone-view h3 {
    font-size: 1.1rem;
}

.mobile-view.small-phone-view p {
    font-size: 0.9rem;
}

/* تعديلات للنصوص في الهواتف الكبيرة */
.mobile-view.large-phone-view h1 {
    font-size: 1.9rem;
}

.mobile-view.large-phone-view h2 {
    font-size: 1.7rem;
}

.mobile-view.large-phone-view h3 {
    font-size: 1.4rem;
}

.mobile-view.large-phone-view p {
    font-size: 1.1rem;
}

/* تعديلات للمدخلات في الهواتف المختلفة */
.mobile-view input,
.mobile-view select,
.mobile-view textarea {
    padding: 0.5rem;
    border-radius: 8px;
    border: 1px solid #ced4da;
}

/* تعديلات للمدخلات في أجهزة iPhone */
.mobile-view.iphone-view input,
.mobile-view.iphone-view select,
.mobile-view.iphone-view textarea {
    border-radius: 10px;
}

/* تعديلات للمدخلات في أجهزة Xiaomi */
.mobile-view.xiaomi-view input,
.mobile-view.xiaomi-view select,
.mobile-view.xiaomi-view textarea {
    border-radius: 6px;
}

/* تعديلات للشاشة السوداء */
@media (prefers-color-scheme: dark) {
    body.mobile-device {
        background-color: #121212;
        color: #f8f9fa;
    }
    
    .mobile-view {
        background-color: #121212;
        color: #f8f9fa;
    }
    
    .mobile-view .card {
        background-color: #1e1e1e;
        border-color: #333;
    }
    
    .mobile-view input,
    .mobile-view select,
    .mobile-view textarea {
        background-color: #2a2a2a;
        border-color: #444;
        color: #f8f9fa;
    }
}
